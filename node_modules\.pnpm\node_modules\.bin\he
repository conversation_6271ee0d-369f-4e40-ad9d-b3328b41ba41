#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../he/bin/he" "$@"
else
  exec node  "$basedir/../he/bin/he" "$@"
fi
