{"version": 3, "file": "reshape-packed.js", "sourceRoot": "", "sources": ["reshape-packed.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAGlC,wCAA0C;AAC1C,gDAAyC;AAEzC,oCAAwF;AAExF,mDAAoD;AAEpD,MAAM,oCAAoC,GAAG,CAAC,aAAgC,EAAE,EAAE,CAAC,CAAC;IAClF,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,CAAC,mBAAW,CAAC,MAAM,CAAC;IAChC,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,SAAS,EAAE,GAAG,aAAa,EAAE;CAC9B,CAAC,CAAC;AAEH,MAAM,gCAAgC,GAAG,CACvC,OAA8B,EAC9B,OAAe,EACf,QAAyB,EACzB,aAAgC,EACnB,EAAE;IACf,MAAM,YAAY,GAAG,OAAO,CAAC,IAAgC,CAAC;IAC9D,MAAM,mBAAmB,GAAG,aAAyC,CAAC;IAEtE,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1B,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,QAAQ,CAAC,EAAE;YACT,KAAK,CAAC;gBACJ,YAAY,GAAG,oBAAoB,CAAC;gBACpC,MAAM;YACR,KAAK,CAAC;gBACJ,YAAY,GAAG,2CAA2C,CAAC;gBAC3D,MAAM;YACR,KAAK,CAAC;gBACJ,YAAY,GAAG,2CAA2C,CAAC;gBAC3D,MAAM;YACR,KAAK,CAAC;gBACJ,YAAY,GAAG,6CAA6C,CAAC;gBAC7D,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,EAAE,CAAC;SACrB;QAED,QAAQ,IAAI;UACN,YAAY;UACZ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qDAAqD,CAAC,CAAC,CAAC,EAAE;;;;;;mBAMzD,CAAC;;UAEV,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;OACnB,CAAC;KACL;IACD,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAEhE,MAAM,YAAY,GAAG;QACf,sBAAsB,CAAC,YAAY,CAAC;QACpC,uBAAuB,CAAC,mBAAmB,CAAC;QAC5C,IAAA,iCAAiB,GAAE;;;;;;;;qBAQN,mBAAmB,CAAC,CAAC,CAAC;qBACtB,mBAAmB,CAAC,CAAC,CAAC;;UAEjC,QAAQ;UACR,IAAI,CAAC,MAAM;;KAEhB,CAAC;IAEJ,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,MAAM,EAAE;QAC1F,YAAY;QACZ,OAAO,EAAE,IAAI;KACd,CAAC;AACJ,CAAC,CAAC;AAEK,MAAM,sCAAsC,GAAG,CACpD,OAA8B,EAC9B,OAAe,EACf,aAAgC,EACb,EAAE;IACrB,MAAM,QAAQ,GAAG,oCAAoC,CAAC,aAAa,CAAC,CAAC;IACrE,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,gCAAgC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,EAAE,CAAC;AACjH,CAAC,CAAC;AAPW,QAAA,sCAAsC,0CAOjD;AAEF,SAAgB,aAAa,CAAC,KAAwB;IACpD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAClB;IACD,wCAAwC;IACxC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QACzC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;KACnB;IACD,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1F,CAAC;AAVD,sCAUC;AAED,yEAAyE;AACzE,wEAAwE;AACxE,yEAAyE;AACzE,uEAAuE;AACvE,0EAA0E;AAC1E,wCAAwC;AACxC,wEAAwE;AACxE,8EAA8E;AAC9E,0EAA0E;AAC1E,2EAA2E;AAC3E,oBAAoB;AACpB,SAAgB,cAAc,CAAC,IAAuB,EAAE,YAA+B;IACrF,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;QAClD,SAAS;QACT,cAAc,GAAG,IAAI,CAAC;KACvB;SAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QACrD,KAAK;QACL,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAClF;SAAM;QACL,OAAO;QACP,cAAc;YACZ,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC/D,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACnE;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAhBD,wCAgBC;AAED,SAAS,sBAAsB,CAAC,KAA+B;IAC7D,MAAM,OAAO,GAAG,gBAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAChD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC/B,MAAM,KAAK,GAAG,OAAO,CAAC;IACtB,MAAM,sBAAsB,GAAG,OAAO;SACnC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjB,MAAM,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,EAAE,CAAC;QACxD,MAAM,KAAK,GACT,CAAC,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC;YACtB,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC,MAAM,MAAM,EAAE;YAC9D,CAAC,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,MAAM,MAAM,EAAE,CAAC;QAC1C,OAAO,GAAG,KAAK,KAAK,KAAK,GAAG,CAAC;IAC/B,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,CAAC;IAEZ,OAAO;;QAED,sBAAsB;;;GAG3B,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAAC,KAA+B;IAC9D,MAAM,OAAO,GAAG,gBAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAEhD,OAAO;;;wBAGe,OAAO,CAAC,CAAC,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC;;CAE5D,CAAC;AACF,CAAC"}