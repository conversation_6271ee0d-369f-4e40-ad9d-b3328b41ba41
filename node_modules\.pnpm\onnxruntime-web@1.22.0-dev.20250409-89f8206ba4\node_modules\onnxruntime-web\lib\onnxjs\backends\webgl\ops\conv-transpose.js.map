{"version": 3, "file": "conv-transpose.js", "sourceRoot": "", "sources": ["conv-transpose.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAgF;AAKhF,gDAAyC;AAEzC,oCAAwF;AAGxF,6CAAuF;AAEvF,MAAM,eAAe,GAAG,CACtB,KAAa,EACb,MAAc,EACd,GAAW,EACX,MAAc,EACd,QAAgB,EAChB,OAAe,EACf,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC;AAExE,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,OAAe,EAAE,IAAc,EAAE,IAAY,EAAE,IAAY,EAAE,EAAE;IAC1G,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IAC1C,IAAI,OAAO,KAAK,YAAY,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;KAClC;SAAM,IAAI,OAAO,KAAK,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;KACvB;AACH,CAAC,CAAC;AAEF,MAAM,2BAA2B,GAAG,CAClC,UAA6B,EAC7B,WAA8B,EAC9B,SAA4B,EAC5B,OAAe,EACf,IAAc,EACd,OAA0B,EAC1B,aAAgC,EAChC,WAAqB,EACrB,EAAE;IACF,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE;QACpC,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC9E,MAAM,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChH,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;QAC/D,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,IAAI,CACd,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAClC,aAAa,CAAC,CAAC,CAAC;gBAChB,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;gBACnC,CAAC;gBACD,IAAI,CAAC,CAAC,CAAC;gBACP,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CACxB,CAAC;SACH;KACF;AACH,CAAC,CAAC;AAOK,MAAM,aAAa,GAAoD,CAC5E,gBAAkC,EAClC,MAAgB,EAChB,UAAmC,EACzB,EAAE;IACZ,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,6CAA6C;IACjF,OAAO,eAAe,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/D,CAAC,CAAC;AAPW,QAAA,aAAa,iBAOxB;AAEF,MAAM,eAAe,GAAoD,CACvE,gBAAuC,EACvC,MAAgB,EAChB,UAAmC,EACzB,EAAE;IACZ,MAAM,kBAAkB,GAAG,kCAAkC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAClF,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC;AAEF,MAAM,kCAAkC,GAAG,CAAC,OAAgB,EAAE,SAAiB,EAAE,EAAE,CAAC,CAAC;IACnF,IAAI,EAAE,eAAe;IACrB,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,UAAU,EAAE,OAAO;QACjB,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;QACpE,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;IAChD,SAAS;CACV,CAAC,CAAC;AAEH,MAAM,sCAAsC,GAAG,CAC7C,gBAAuC,EACvC,MAAyB,EACzB,QAAyB,EACzB,UAAmC,EACtB,EAAE;IACf,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACzC,MAAM,qBAAqB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;IAC3D,MAAM,WAAW,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;IACzG,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,IAAA,iCAAoB,EAAC,UAAU,CAAC,CAAC;IAEjF,MAAM,YAAY,GAAG;gCACS,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;6BAClD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,kBAAkB;;;;;;;;sCAQgB,sBAAsB;oDACR,sBAAsB;;oBAEtD,SAAS;sDACyB,qBAAqB;uCACpC,qBAAqB;oCACxB,MAAM,CAAC,CAAC,CAAC;sCACP,MAAM,CAAC,CAAC,CAAC;uCACR,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;;;;;0CAKxD,MAAM,CAAC,CAAC,CAAC;0CACT,MAAM,CAAC,CAAC,CAAC;;;;;;;;;MAS7C,eAAe;MACf,IAAI,CAAC,MAAM;;CAEhB,CAAC;IACA,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,YAAY;QACZ,OAAO,EAAE,IAAI;KACd,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,4CAA4C,GAAG,CACnD,gBAAuC,EACvC,MAAyB,EACzB,UAAmC,EAChB,EAAE;IACrB,MAAM,QAAQ,GAAG,kCAAkC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC5F,OAAO;QACL,GAAG,QAAQ;QACX,GAAG,EAAE,GAAG,EAAE,CAAC,sCAAsC,CAAC,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;KAClG,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,gBAAuC,EACvC,MAAyB,EACzB,UAAmC,EAC3B,EAAE;IACV,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CACjC,4CAA4C,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAClF,MAAM,CACP,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,kCAAkC,GAAG,CAAoC,UAAa,EAAE,MAAgB,EAAK,EAAE;IACnH,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACnD,qGAAqG;IACrG,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC9C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;KACF;IAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACrC,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACnD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClC,6FAA6F;IAC7F,uDAAuD;IACvD,2BAA2B,CACzB,UAAU,EACV,WAAW,EACX,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,OAAO,EAClB,IAAI,EACJ,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,aAAa,EACxB,WAAW,CACZ,CAAC;IAEF,wEAAwE;IACxE,MAAM,aAAa,GAAM,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChG,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AAEK,MAAM,4BAA4B,GAAoD,CAC3F,IAAgB,EACS,EAAE;IAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,MAAM,oBAAoB,GAAG,IAAA,8CAAiC,EAAC,UAAU,CAAC,CAAC;IAC3E,2FAA2F;IAC3F,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC5C,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnE,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAEtD,OAAO,IAAA,sDAA2B,EAAC;QACjC,OAAO;QACP,SAAS;QACT,KAAK;QACL,WAAW;QACX,aAAa;QACb,WAAW;QACX,IAAI;QACJ,OAAO;QACP,GAAG,oBAAoB;KACxB,CAAC,CAAC;AACL,CAAC,CAAC;AA1BW,QAAA,4BAA4B,gCA0BvC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAE,UAAmC,EAAQ,EAAE;IACrF,+CAA+C;IAC/C,gEAAgE;IAChE,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;QAC3D,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,wDAAwD;IACxD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IAED,oDAAoD;IACpD,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,WAAW,KAAK,eAAe,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;KACtE;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;IAEzD,+GAA+G;IAC/G,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE;QAC7F,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;KACjC;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,4BAA4B;IAC5B,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,WAAW,EAAE;QAC/C,MAAM,IAAI,KAAK,CAAC,uBAAuB,WAAW,GAAG,CAAC,CAAC;KACxD;IAED,0BAA0B;IAC1B,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,qBAAqB,WAAW,GAAG,CAAC,CAAC;KACtD;IAED,uBAAuB;IACvB,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,GAAG,CAAC,EAAE;QAC9C,MAAM,IAAI,KAAK,CAAC,kBAAkB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;KACvD;IAED,iCAAiC;IACjC,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE;QACnD,MAAM,IAAI,KAAK,CAAC,4BAA4B,WAAW,GAAG,CAAC,CAAC;KAC7D;IAED,sGAAsG;IACtG,iDAAiD;IACjD,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACtG,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,sEAAsE;IACtE,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACtG,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,yCAAyC;IACzC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QACvD,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;AACH,CAAC,CAAC"}