'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.RuntimeOptimizationRecordContainerEntry = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const runtime_optimization_record_js_1 = require('../../onnxruntime/fbs/runtime-optimization-record.js');
class RuntimeOptimizationRecordContainerEntry {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsRuntimeOptimizationRecordContainerEntry(bb, obj) {
    return (obj || new RuntimeOptimizationRecordContainerEntry()).__init(
      bb.readInt32(bb.position()) + bb.position(),
      bb,
    );
  }
  static getSizePrefixedRootAsRuntimeOptimizationRecordContainerEntry(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new RuntimeOptimizationRecordContainerEntry()).__init(
      bb.readInt32(bb.position()) + bb.position(),
      bb,
    );
  }
  optimizerName(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  runtimeOptimizationRecords(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset
      ? (obj || new runtime_optimization_record_js_1.RuntimeOptimizationRecord()).__init(
          this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4),
          this.bb,
        )
      : null;
  }
  runtimeOptimizationRecordsLength() {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startRuntimeOptimizationRecordContainerEntry(builder) {
    builder.startObject(2);
  }
  static addOptimizerName(builder, optimizerNameOffset) {
    builder.addFieldOffset(0, optimizerNameOffset, 0);
  }
  static addRuntimeOptimizationRecords(builder, runtimeOptimizationRecordsOffset) {
    builder.addFieldOffset(1, runtimeOptimizationRecordsOffset, 0);
  }
  static createRuntimeOptimizationRecordsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startRuntimeOptimizationRecordsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static endRuntimeOptimizationRecordContainerEntry(builder) {
    const offset = builder.endObject();
    builder.requiredField(offset, 4); // optimizer_name
    return offset;
  }
  static createRuntimeOptimizationRecordContainerEntry(builder, optimizerNameOffset, runtimeOptimizationRecordsOffset) {
    RuntimeOptimizationRecordContainerEntry.startRuntimeOptimizationRecordContainerEntry(builder);
    RuntimeOptimizationRecordContainerEntry.addOptimizerName(builder, optimizerNameOffset);
    RuntimeOptimizationRecordContainerEntry.addRuntimeOptimizationRecords(builder, runtimeOptimizationRecordsOffset);
    return RuntimeOptimizationRecordContainerEntry.endRuntimeOptimizationRecordContainerEntry(builder);
  }
}
exports.RuntimeOptimizationRecordContainerEntry = RuntimeOptimizationRecordContainerEntry;
//# sourceMappingURL=runtime-optimization-record-container-entry.js.map
