const createTransformersWorker = (createOptions) => {
  let worker;
  let isReady = false;
  let isLoading = false;
  const load = async (payload, options) => {
    if (!payload)
      throw new Error("Payload is required");
    return new Promise((resolve, reject) => {
      if (!createOptions.workerURL)
        throw new Error("Worker URL is required");
      if (isReady) {
        resolve();
        return;
      }
      try {
        let onProgress;
        if (options?.onProgress != null) {
          onProgress = options?.onProgress;
          delete options?.onProgress;
        }
        if (!isLoading && !isReady && !worker) {
          let workerURLString;
          if (createOptions.workerURL instanceof URL) {
            const workerURL = new URL(createOptions.workerURL);
            workerURLString = workerURL.searchParams.get("worker-url");
          } else {
            workerURLString = createOptions.workerURL;
          }
          if (!workerURLString)
            throw new Error("Worker URL is required");
          if (!worker)
            worker = new Worker(workerURLString, { type: "module" });
          if (!worker)
            throw new Error("Worker not initialized");
          worker.postMessage(payload);
          worker.addEventListener("message", (event) => {
            switch (event.data.type) {
              case "error":
                isLoading = false;
                reject(event.data.data.error);
                break;
              case "progress":
                if (onProgress != null && typeof onProgress === "function") {
                  onProgress(event.data.data.progress);
                }
                break;
            }
          });
        }
        worker.addEventListener("message", (event) => {
          if (event.data.type !== "status" || event.data.data.status !== "ready")
            return;
          isReady = true;
          isLoading = false;
          resolve();
        });
      } catch (err) {
        isLoading = false;
        reject(err);
      }
    });
  };
  const ensureLoadBeforeProcess = async (options) => {
    if (options != null && !options?.loadOptions)
      await load(options?.loadOptions?.payload, options?.loadOptions?.options);
  };
  const process = (payload, onResultType, options) => {
    return new Promise((resolve, reject) => {
      ensureLoadBeforeProcess(options).then(() => {
        if (!worker || !isReady) {
          reject(new Error("Model not loaded"));
          return;
        }
        worker.addEventListener("error", (event) => {
          reject(event);
        });
        let errored = false;
        let resultDone = false;
        worker.addEventListener("message", (event) => {
          switch (event.data.type) {
            case "error":
              errored = true;
              reject(event.data.data?.error);
              break;
            case onResultType:
              resultDone = true;
              resolve(event.data.data);
              break;
          }
        });
        if (!errored && !resultDone) {
          worker?.postMessage(payload);
        }
      });
    });
  };
  return {
    dispose: () => {
      if (!worker)
        return;
      worker.terminate();
      isReady = false;
      isLoading = false;
      worker = void 0;
    },
    load,
    process
  };
};

export { createTransformersWorker };
