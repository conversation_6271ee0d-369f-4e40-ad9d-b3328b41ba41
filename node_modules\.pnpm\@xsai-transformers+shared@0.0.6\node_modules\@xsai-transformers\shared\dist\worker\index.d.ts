import { ProgressInfo } from '@huggingface/transformers';
import { LoadOptionProgressCallback } from '../types/index.js';
import 'onnxruntime-common';

type ErrorMessageEvents<E = unknown> = WorkerMessageEvent<{
    error?: E;
    message?: string;
}, 'error'>;
type LoadMessageEvents<D = undefined, T extends string = string, E = unknown> = ErrorMessageEvents<E> | WorkerMessageEvent<D, T> | WorkerMessageEvent<{
    message: string;
}, 'info'> | WorkerMessageEvent<{
    message?: string;
    status: 'loading' | 'ready';
}, 'status'> | WorkerMessageEvent<{
    progress: ProgressInfo;
}, 'progress'>;
type ProcessMessageEvents<D = unknown, T = unknown, E = unknown> = ErrorMessageEvents<E> | WorkerMessageEvent<D, T>;
interface WorkerMessageEvent<D, T> {
    data: D;
    type: T;
}
type WorkerMessageEvents<D = undefined, T extends string = string> = LoadMessageEvents<D, T> | ProcessMessageEvents<D, T>;
declare const createTransformersWorker: <T extends {
    workerURL: string | undefined | URL;
}, T2 extends {
    onProgress?: LoadOptionProgressCallback;
}>(createOptions: T) => {
    dispose: () => void;
    load: <D = unknown, E extends WorkerMessageEvent<D, string> = WorkerMessageEvent<D, string>>(payload?: E, options?: T2) => Promise<void>;
    process: <ID = unknown, OD = unknown, E_1 extends {
        data: ID;
        type: string;
    } = {
        data: any;
        type: string;
    }>(payload: E_1, onResultType: string, options?: T2 & {
        loadOptions?: {
            options?: T2;
            payload: LoadMessageEvents<any, string>;
        };
    }) => Promise<OD>;
};

export { type ErrorMessageEvents, type LoadMessageEvents, type ProcessMessageEvents, type WorkerMessageEvent, type WorkerMessageEvents, createTransformersWorker };
