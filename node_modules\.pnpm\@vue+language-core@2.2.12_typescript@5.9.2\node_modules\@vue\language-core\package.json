{"name": "@vue/language-core", "version": "2.2.12", "license": "MIT", "files": ["**/*.js", "**/*.d.ts"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.15", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.15", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "0b13bf1966398ea3949b6b02d09b251ddc9a51eb"}