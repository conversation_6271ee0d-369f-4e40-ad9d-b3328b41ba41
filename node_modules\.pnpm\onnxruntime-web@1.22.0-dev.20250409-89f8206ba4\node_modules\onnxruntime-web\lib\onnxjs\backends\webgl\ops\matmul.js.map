{"version": 3, "file": "matmul.js", "sourceRoot": "", "sources": ["matmul.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAKlC,wCAAyD;AAEzD,oCAAwF;AACxF,oCAA4D;AAE5D,6CAAqH;AACrH,+CAAoE;AAE7D,MAAM,MAAM,GAAyD,CAC1E,gBAAuC,EACvC,MAAgB,EAChB,UAAwC,EAC9B,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE;QACjC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAA,iDAAmC,EAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KAClH;SAAM;QACL,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,6BAA6B,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KAC1F;AACH,CAAC,CAAC;AAZW,QAAA,MAAM,UAYjB;AAEK,MAAM,qBAAqB,GAAyD,CACzF,IAAgB,EACc,EAAE,CAAC,IAAA,8CAAiC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAFzE,QAAA,qBAAqB,yBAEoD;AAEtF,MAAM,2BAA2B,GAAG,CAAC,OAAgB,EAAE,SAAiB,EAAE,EAAE,CAAC,CAAC;IAC5E,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IACrD,UAAU,EAAE,OAAO;QACjB,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;QACpE,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;IAChD,SAAS;CACV,CAAC,CAAC;AAEH,SAAS,uBAAuB,CAC9B,QAAyB,EACzB,MAAgB,EAChB,oBAAkD;IAElD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,WAAW,GAAG,oBAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAClE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;KAC1D;IACD,MAAM,cAAc,GAAG,IAAA,yBAAiB,EAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC7D,MAAM,aAAa,GAAG,IAAA,qBAAa,GAAE,CAAC;IACtC,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,IAAA,iCAAoB,EAAC,oBAAoB,CAAC,CAAC;IAE3F,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE,MAAM,uBAAuB,GAAG,OAAO;QACrC,CAAC,CAAC,GAAG,gBAAgB,CAAC,cAAc,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE;QAC1F,CAAC,CAAC,EAAE,CAAC;IAEP,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM,YAAY,GAAG;MACjB,kBAAkB;MAClB,uBAAuB;gCACG,IAAI;gBACpB,KAAK;gBACL,KAAK;;;;;0BAKK,SAAS;gBACnB,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG,CAAC;;;UAGf,WAAW;UACX,eAAe;;MAEnB,CAAC;IACL,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,YAAY;KACb,CAAC;AACJ,CAAC;AAED,SAAgB,6BAA6B,CAC3C,MAAgB,EAChB,oBAAkD;IAElD,MAAM,QAAQ,GAAG,2BAA2B,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;IACzG,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,oBAAoB,CAAC,EAAE,CAAC;AACrG,CAAC;AAND,sEAMC;AAED,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;QAC3F,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACrD;IAED,IACE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC;QAC9D,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,EAC9D;QACA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;AACH,CAAC,CAAC;AAEF,SAAgB,gBAAgB,CAC9B,cAAsB,EACtB,aAAgC,EAChC,OAA0B,EAC1B,QAA2B,EAC3B,QAAiB;IAEjB,IAAI,qBAAqB,GAAG,EAAE,CAAC;IAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAChC,MAAM,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;IAClC,IAAI,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QAC7B,qBAAqB,GAAG,QAAQ,CAAC;KAClC;SAAM;QACL,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACpG;IACD,MAAM,aAAa,GAAG,oBAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxE,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxG,MAAM,MAAM,GAAG,gBAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,CAAC;IACnC,IAAI,MAAM,GAAG,sCAAsC,CAAC;IACpD,IAAI,aAAa,EAAE;QACjB,MAAM,GAAG,qBAAqB,CAAC;KAChC;IACD,MAAM,sBAAsB,GAAG,QAAQ;QACrC,CAAC,CAAC;;IAEF,cAAc;IACd,aAAa;+BACc,qBAAqB;WACzC,MAAM;EACf;QACE,CAAC,CAAC;;IAEF,cAAc;IACd,aAAa;;EAEf,CAAC;IAED,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAxCD,4CAwCC"}