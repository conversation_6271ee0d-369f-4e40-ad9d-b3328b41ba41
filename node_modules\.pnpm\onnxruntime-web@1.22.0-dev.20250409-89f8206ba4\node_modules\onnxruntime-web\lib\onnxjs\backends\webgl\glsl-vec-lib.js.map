{"version": 3, "file": "glsl-vec-lib.js", "sourceRoot": "", "sources": ["glsl-vec-lib.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,yDAA0E;AAE1E;;;;GAIG;AACH,MAAa,UAAW,SAAQ,0BAAO;IACrC,YAAY,OAAoB;QAC9B,KAAK,CAAC,OAAO,CAAC,CAAC;IACjB,CAAC;IACD,cAAc;QACZ,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,YAAY;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;IACzG,CAAC;IACS,kBAAkB;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACtD,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC;QACvC,MAAM,MAAM,GAA+B,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QAC1F,MAAM,MAAM,GAAuC,EAAE,CAAC;QACtD,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;YACzB,MAAM,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC;YAC3B,IAAI,eAAe,GAAG,EAAE,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;gBAC7B,eAAe,IAAI;iBACV,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;WACjC,CAAC;aACL;YACD,MAAM,IAAI,GAAG;eACJ,KAAK,YAAY,IAAI,mBAAmB,IAAI;YAC/C,eAAe;;SAElB,CAAC;YACJ,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,iCAAc,CAAC,IAAI,CAAC,CAAC;SAC1C;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IACS,OAAO;QACf,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACtD,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC;QACvC,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;YAC7B,eAAe,IAAI;eACV,CAAC,WAAW,CAAC;SACnB,CAAC;SACL;QACD,MAAM,IAAI,GAAG;6BACY,IAAI,mBAAmB,IAAI;UAC9C,eAAe;;OAElB,CAAC;QACJ,OAAO,EAAE,OAAO,EAAE,IAAI,iCAAc,CAAC,IAAI,CAAC,EAAE,CAAC;IAC/C,CAAC;IAES,UAAU;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACtD,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC;QACvC,IAAI,KAAK,GAAG;;qBAEK,IAAI;;;SAGhB,CAAC;QACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YACjC,KAAK,IAAI;4BACa,CAAC;gBACb,CAAC;aACJ,CAAC;SACT;QACD,KAAK,IAAI;;gBAEG,IAAI,GAAG,CAAC;SACf,CAAC;QACN,MAAM,IAAI,GAAG;kCACiB,IAAI;UAC5B,KAAK;;SAEN,CAAC;QACN,OAAO,EAAE,UAAU,EAAE,IAAI,iCAAc,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD,CAAC;IACS,UAAU;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACtD,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC;QACvC,IAAI,KAAK,GAAG;;sBAEM,IAAI;;;OAGnB,CAAC;QACJ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YACjC,KAAK,IAAI;4BACa,CAAC;uBACN,CAAC;OACjB,CAAC;SACH;QACD,KAAK,IAAI;;uBAEU,IAAI,GAAG,CAAC;SACtB,CAAC;QACN,MAAM,IAAI,GAAG;6BACY,IAAI;UACvB,KAAK;;KAEV,CAAC;QACF,OAAO,EAAE,UAAU,EAAE,IAAI,iCAAc,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD,CAAC;CACF;AAtGD,gCAsGC"}