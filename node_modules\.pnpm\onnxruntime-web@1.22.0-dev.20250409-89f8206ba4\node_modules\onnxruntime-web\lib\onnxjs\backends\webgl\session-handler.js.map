{"version": 3, "file": "session-handler.js", "sourceRoot": "", "sources": ["session-handler.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAIlC,iDAA0C;AAE1C,uCAAqD;AAKrD,2DAA4D;AAC5D,yDAA4D;AAC5D,uDAAmD;AACnD,uEAAyF;AACzF,uDAAmD;AAGnD,MAAa,mBAAmB;IAW9B,YACkB,OAAqB,EACrB,OAAwB;QADxB,YAAO,GAAP,OAAO,CAAc;QACrB,YAAO,GAAP,OAAO,CAAiB;QAExC,IAAI,CAAC,cAAc,GAAG,IAAI,+CAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACtG,aAAa,EAAE,OAAO,CAAC,gBAAgB,KAAK,MAAM;SACnD,CAAC,CAAC;QACH,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;IAClC,CAAC;IAED,sBAAsB;QACpB,OAAO,IAAI,yCAAqB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IACD,kBAAkB,CAAC,KAAY;QAC7B,MAAM,YAAY,GAAG,KAAK;aACvB,SAAS,EAAE;aACX,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;aACxC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IACD,aAAa,CAAC,QAAmB;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACrE,CAAC;IACD,cAAc,CAAC,QAAmB;QAChC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IACD,cAAc,CAAC,QAAmB,EAAE,QAAiB;QACnD,IAAI,QAAQ,EAAE;YACZ,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAClD;aAAM;YACL,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACpD;IACH,CAAC;IACD,cAAc,CAAC,QAAmB,EAAE,WAAwB,EAAE,QAAQ,GAAG,KAAK;QAC5E,mBAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,+BAA+B,CAAC,CAAC;QACvE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;SACxD;aAAM;YACL,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;SAC1D;IACH,CAAC;IACD,OAAO;QACL,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;QAC1C,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1F,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAC5F,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;IAC5C,CAAC;IACD,OAAO,CAAC,IAAgB,EAAE,MAAwB,EAAE,KAAY;QAC9D,MAAM,EAAE,GAAG,IAAA,uBAAe,EAAC,IAAI,EAAE,MAAM,EAAE,yCAAsB,CAAC,CAAC;QACjE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACjF,CAAC;CACF;AAtED,kDAsEC"}