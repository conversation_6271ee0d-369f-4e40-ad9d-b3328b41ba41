'use strict';
// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.
Object.defineProperty(exports, '__esModule', { value: true });
exports.TextureType = void 0;
var TextureType;
(function (TextureType) {
  TextureType[(TextureType['unpacked'] = 0)] = 'unpacked';
  TextureType[(TextureType['unpackedReversed'] = 1)] = 'unpackedReversed';
  TextureType[(TextureType['packed'] = 2)] = 'packed';
  TextureType[(TextureType['downloadUint8AsFloat'] = 3)] = 'downloadUint8AsFloat';
  TextureType[(TextureType['packedLastDimension'] = 4)] = 'packedLastDimension';
})(TextureType || (exports.TextureType = TextureType = {}));
//# sourceMappingURL=types.js.map
