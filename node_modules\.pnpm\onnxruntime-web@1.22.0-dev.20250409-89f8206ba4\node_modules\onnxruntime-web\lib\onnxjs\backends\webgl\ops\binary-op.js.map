{"version": 3, "file": "binary-op.js", "sourceRoot": "", "sources": ["binary-op.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAGlC,wCAAyD;AACzD,0DAAsE;AACtE,gDAAyC;AAEzC,oCAAuE;AAEvE,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,0BAWC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,0BAWC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,0BAWC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,0BAWC;AACD,SAAgB,SAAS;IACvB,MAAM,IAAI,GAAG,QAAQ,CAAC;IACtB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,8BAWC;AACD,SAAgB,WAAW;IACzB,MAAM,IAAI,GAAG,UAAU,CAAC;IACxB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;;;;GAMV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAdD,kCAcC;AACD,SAAgB,QAAQ;IACtB,MAAM,IAAI,GAAG,OAAO,CAAC;IACrB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;;;;GAMV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAdD,4BAcC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;;;;;;GAQV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAhBD,0BAgBC;AACD,SAAgB,MAAM;IACpB,MAAM,IAAI,GAAG,KAAK,CAAC;IACnB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;;;;;;GAQV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAhBD,wBAgBC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;;;;;;GAQV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAhBD,0BAgBC;AACD,SAAgB,OAAO;IACrB,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAClC,CAAC;AAFD,0BAEC;AACD,SAAgB,SAAS;IACvB,MAAM,IAAI,GAAG,QAAQ,CAAC;IACtB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;;;;;;GAQV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAhBD,8BAgBC;AAED,SAAS,iBAAiB,CAAC,KAAa;IACtC,MAAM,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC;IACzB,MAAM,IAAI,GAAG;UACL,IAAI;aACD,KAAK;;SAET,IAAI;aACA,KAAK;;GAEf,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAED,MAAM,6BAA6B,GAAG,CACpC,OAA8B,EAC9B,MAAgB,EAChB,QAA2B,EAC3B,mBAAoC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAClD,QAAiB,EACE,EAAE;IACrB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,CAAC;IACrF,OAAO;QACL,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACtB,UAAU,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;QACtC,SAAS,EAAE,QAAQ;QACnB,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,CAAC;KAChF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,OAA8B,EAC9B,MAAgB,EAChB,QAA2B,EAC3B,mBAAoC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EACrC,EAAE;IACf,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,CAAC;IACrF,MAAM,WAAW,GAAG,CAAC,gBAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACxE,IAAI,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEjC,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;IAE9C,IAAI,WAAW,EAAE;QACf,MAAM,eAAe,GAAG,oBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACvF,IAAI,CAAC,eAAe,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;SACjE;QACD,WAAW,GAAG,eAAe,CAAC;QAC9B,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC,CAAC,CAAC,kBAAkB,CAAC;QACvG,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAEvG,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAChE,MAAM,YAAY,GAAG,gBAAgB;YACnC,CAAC,CAAC;QACA,QAAQ,CAAC,IAAI;;;;wBAIG,QAAQ,CAAC,IAAI;UAC3B,IAAI,CAAC,MAAM;QACb;YACF,CAAC,CAAC;QACA,QAAQ,CAAC,IAAI;kCACa,UAAU;uBACrB,KAAK;uBACL,KAAK;UAClB,MAAM;UACN,MAAM;iBACC,QAAQ,CAAC,IAAI;QACtB,CAAC;QAEL,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;YACtB,UAAU,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACtC,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE;YAClE,YAAY;YACZ,OAAO,EAAE,gBAAgB;SAC1B,CAAC;KACH;IACD,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAChE,MAAM,YAAY,GAAG;MACjB,QAAQ,CAAC,IAAI;;kBAED,IAAI,CAAC,SAAS;kBACd,IAAI,CAAC,SAAS;sBACV,QAAQ,CAAC,IAAI;QAC3B,IAAI,CAAC,MAAM;;KAEd,CAAC;IAEJ,OAAO;QACL,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACtB,UAAU,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;QACtC,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE;QACrE,YAAY;QACZ,OAAO,EAAE,IAAI;KACd,CAAC;AACJ,CAAC,CAAC;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CAC/E,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CAC/E,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,KAAK,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;CACzF,CAAC;AAFW,QAAA,KAAK,SAEhB;AAEK,MAAM,OAAO,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACrF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;CAC3F,CAAC;AAFW,QAAA,OAAO,WAElB;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CAC/E,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,EAAE,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;CACtF,CAAC;AAFW,QAAA,EAAE,MAEb;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CAC/E,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,KAAK,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC;CACjF,CAAC;AAFW,QAAA,KAAK,SAEhB;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CAC/E,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd"}