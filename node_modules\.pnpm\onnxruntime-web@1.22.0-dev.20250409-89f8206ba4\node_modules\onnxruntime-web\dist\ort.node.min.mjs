/*!
 * ONNX Runtime Web v1.22.0-dev.20250409-89f8206ba4
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import{createRequire}from"module";const require=createRequire(import.meta.url);
var pe=Object.defineProperty;var bt=Object.getOwnPropertyDescriptor;var gt=Object.getOwnPropertyNames;var wt=Object.prototype.hasOwnProperty;var de=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var C=(e,t)=>()=>(e&&(t=e(e=0)),t);var yt=(e,t)=>{for(var n in t)pe(e,n,{get:t[n],enumerable:!0})},St=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of gt(t))!wt.call(e,r)&&r!==n&&pe(e,r,{get:()=>t[r],enumerable:!(o=bt(t,r))||o.enumerable});return e};var ht=e=>St(pe({},"__esModule",{value:!0}),e);var W,re=C(()=>{"use strict";W=!!(typeof process<"u"&&process.versions&&process.versions.node)});var xe,Et,Ot,F,Be,_e,Tt,vt,Pt,It,Ue,Ae,me=C(()=>{"use strict";re();xe=W||typeof location>"u"?void 0:location.origin,Et=import.meta.url>"file:"&&import.meta.url<"file;",Ot=()=>{if(!W){if(Et){let e=URL;return new URL(new e("ort.node.min.mjs",import.meta.url).href,xe).href}return import.meta.url}},F=Ot(),Be=()=>{if(F&&!F.startsWith("blob:"))return F.substring(0,F.lastIndexOf("/")+1)},_e=(e,t)=>{try{let n=t??F;return(n?new URL(e,n):new URL(e)).origin===xe}catch{return!1}},Tt=(e,t)=>{let n=t??F;try{return(n?new URL(e,n):new URL(e)).href}catch{return}},vt=(e,t)=>`${t??"./"}${e}`,Pt=async e=>{let n=await(await fetch(e,{credentials:"same-origin"})).blob();return URL.createObjectURL(n)},It=async e=>(await import(/*webpackIgnore:true*/e)).default,Ue=void 0,Ae=async(e,t,n)=>{if(!e&&!t&&Ue&&F&&_e(F))return[void 0,Ue];{let o="ort-wasm-simd-threaded.mjs",r=e??Tt(o,t),a=!W&&n&&r&&!_e(r,t),s=a?await Pt(r):r??vt(o,t);return[a?s:void 0,await It(s)]}}});var be,ge,ne,Me,Lt,_t,Ut,Ce,y,H=C(()=>{"use strict";me();ge=!1,ne=!1,Me=!1,Lt=()=>{if(typeof SharedArrayBuffer>"u")return!1;try{return typeof MessageChannel<"u"&&new MessageChannel().port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11]))}catch{return!1}},_t=()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,30,1,28,0,65,0,253,15,253,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,253,186,1,26,11]))}catch{return!1}},Ut=()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,19,1,17,0,65,1,253,15,65,2,253,15,65,3,253,15,253,147,2,11]))}catch{return!1}},Ce=async e=>{if(ge)return Promise.resolve();if(ne)throw new Error("multiple calls to 'initializeWebAssembly()' detected.");if(Me)throw new Error("previous call to 'initializeWebAssembly()' failed.");ne=!0;let t=e.initTimeout,n=e.numThreads;if(e.simd!==!1){if(e.simd==="relaxed"){if(!Ut())throw new Error("Relaxed WebAssembly SIMD is not supported in the current environment.")}else if(!_t())throw new Error("WebAssembly SIMD is not supported in the current environment.")}let o=Lt();n>1&&!o&&(typeof self<"u"&&!self.crossOriginIsolated&&console.warn("env.wasm.numThreads is set to "+n+", but this will not work unless you enable crossOriginIsolated mode. See https://web.dev/cross-origin-isolation-guide/ for more info."),console.warn("WebAssembly multi-threading is not supported in the current environment. Falling back to single-threading."),e.numThreads=n=1);let r=e.wasmPaths,a=typeof r=="string"?r:void 0,s=r?.mjs,i=s?.href??s,c=r?.wasm,l=c?.href??c,f=e.wasmBinary,[d,u]=await Ae(i,a,n>1),p=!1,S=[];if(t>0&&S.push(new Promise(O=>{setTimeout(()=>{p=!0,O()},t)})),S.push(new Promise((O,L)=>{let m={numThreads:n};if(f)m.wasmBinary=f;else if(l||a)m.locateFile=g=>l??a+g;else if(i&&i.indexOf("blob:")!==0)m.locateFile=g=>new URL(g,i).href;else if(d){let g=Be();g&&(m.locateFile=D=>g+D)}u(m).then(g=>{ne=!1,ge=!0,be=g,O(),d&&URL.revokeObjectURL(d)},g=>{ne=!1,Me=!0,L(g)})})),await Promise.race(S),p)throw new Error(`WebAssembly backend initializing failed due to timeout: ${t}ms`)},y=()=>{if(ge&&be)return be;throw new Error("WebAssembly is not initialized yet.")}});var A,Z,w,oe=C(()=>{"use strict";H();A=(e,t)=>{let n=y(),o=n.lengthBytesUTF8(e)+1,r=n._malloc(o);return n.stringToUTF8(e,r,o),t.push(r),r},Z=(e,t,n,o)=>{if(typeof e=="object"&&e!==null){if(n.has(e))throw new Error("Circular reference in options");n.add(e)}Object.entries(e).forEach(([r,a])=>{let s=t?t+r:r;if(typeof a=="object")Z(a,s+".",n,o);else if(typeof a=="string"||typeof a=="number")o(s,a.toString());else if(typeof a=="boolean")o(s,a?"1":"0");else throw new Error(`Can't handle extra config type: ${typeof a}`)})},w=e=>{let t=y(),n=t.stackSave();try{let o=t.PTR_SIZE,r=t.stackAlloc(2*o);t._OrtGetLastError(r,r+o);let a=Number(t.getValue(r,o===4?"i32":"i64")),s=t.getValue(r+o,"*"),i=s?t.UTF8ToString(s):"";throw new Error(`${e} ERROR_CODE: ${a}, ERROR_MESSAGE: ${i}`)}finally{t.stackRestore(n)}}});var De,ke=C(()=>{"use strict";H();oe();De=e=>{let t=y(),n=0,o=[],r=e||{};try{if(e?.logSeverityLevel===void 0)r.logSeverityLevel=2;else if(typeof e.logSeverityLevel!="number"||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);if(e?.logVerbosityLevel===void 0)r.logVerbosityLevel=0;else if(typeof e.logVerbosityLevel!="number"||!Number.isInteger(e.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);e?.terminate===void 0&&(r.terminate=!1);let a=0;return e?.tag!==void 0&&(a=A(e.tag,o)),n=t._OrtCreateRunOptions(r.logSeverityLevel,r.logVerbosityLevel,!!r.terminate,a),n===0&&w("Can't create run options."),e?.extra!==void 0&&Z(e.extra,"",new WeakSet,(s,i)=>{let c=A(s,o),l=A(i,o);t._OrtAddRunConfigEntry(n,c,l)!==0&&w(`Can't set a run config entry: ${s} - ${i}.`)}),[n,o]}catch(a){throw n!==0&&t._OrtReleaseRunOptions(n),o.forEach(s=>t._free(s)),a}}});var xt,Bt,At,se,Mt,We,Fe=C(()=>{"use strict";H();oe();xt=e=>{switch(e){case"disabled":return 0;case"basic":return 1;case"extended":return 2;case"all":return 99;default:throw new Error(`unsupported graph optimization level: ${e}`)}},Bt=e=>{switch(e){case"sequential":return 0;case"parallel":return 1;default:throw new Error(`unsupported execution mode: ${e}`)}},At=e=>{e.extra||(e.extra={}),e.extra.session||(e.extra.session={});let t=e.extra.session;t.use_ort_model_bytes_directly||(t.use_ort_model_bytes_directly="1"),e.executionProviders&&e.executionProviders.some(n=>(typeof n=="string"?n:n.name)==="webgpu")&&(e.enableMemPattern=!1)},se=(e,t,n,o)=>{let r=A(t,o),a=A(n,o);y()._OrtAddSessionConfigEntry(e,r,a)!==0&&w(`Can't set a session config entry: ${t} - ${n}.`)},Mt=async(e,t,n)=>{for(let o of t){let r=typeof o=="string"?o:o.name,a=[];switch(r){case"webnn":if(r="WEBNN",typeof o!="string"){let d=o?.deviceType;d&&se(e,"deviceType",d,n)}break;case"webgpu":if(r="JS",typeof o!="string"){let f=o;if(f?.preferredLayout){if(f.preferredLayout!=="NCHW"&&f.preferredLayout!=="NHWC")throw new Error(`preferredLayout must be either 'NCHW' or 'NHWC': ${f.preferredLayout}`);se(e,"preferredLayout",f.preferredLayout,n)}}break;case"wasm":case"cpu":continue;default:throw new Error(`not supported execution provider: ${r}`)}let s=A(r,n),i=a.length,c=0,l=0;if(i>0){c=y()._malloc(i*y().PTR_SIZE),n.push(c),l=y()._malloc(i*y().PTR_SIZE),n.push(l);for(let f=0;f<i;f++)y().setValue(c+f*y().PTR_SIZE,a[f][0],"*"),y().setValue(l+f*y().PTR_SIZE,a[f][1],"*")}await y()._OrtAppendExecutionProvider(e,s,c,l,i)!==0&&w(`Can't append execution provider: ${r}.`)}},We=async e=>{let t=y(),n=0,o=[],r=e||{};At(r);try{let a=xt(r.graphOptimizationLevel??"all"),s=Bt(r.executionMode??"sequential"),i=typeof r.logId=="string"?A(r.logId,o):0,c=r.logSeverityLevel??2;if(!Number.isInteger(c)||c<0||c>4)throw new Error(`log serverity level is not valid: ${c}`);let l=r.logVerbosityLevel??0;if(!Number.isInteger(l)||l<0||l>4)throw new Error(`log verbosity level is not valid: ${l}`);let f=typeof r.optimizedModelFilePath=="string"?A(r.optimizedModelFilePath,o):0;if(n=t._OrtCreateSessionOptions(a,!!r.enableCpuMemArena,!!r.enableMemPattern,s,!!r.enableProfiling,0,i,c,l,f),n===0&&w("Can't create session options."),r.executionProviders&&await Mt(n,r.executionProviders,o),r.enableGraphCapture!==void 0){if(typeof r.enableGraphCapture!="boolean")throw new Error(`enableGraphCapture must be a boolean value: ${r.enableGraphCapture}`);se(n,"enableGraphCapture",r.enableGraphCapture.toString(),o)}if(r.freeDimensionOverrides)for(let[d,u]of Object.entries(r.freeDimensionOverrides)){if(typeof d!="string")throw new Error(`free dimension override name must be a string: ${d}`);if(typeof u!="number"||!Number.isInteger(u)||u<0)throw new Error(`free dimension override value must be a non-negative integer: ${u}`);let p=A(d,o);t._OrtAddFreeDimensionOverride(n,p,u)!==0&&w(`Can't set a free dimension override: ${d} - ${u}.`)}return r.extra!==void 0&&Z(r.extra,"",new WeakSet,(d,u)=>{se(n,d,u,o)}),[n,o]}catch(a){throw n!==0&&t._OrtReleaseSessionOptions(n)!==0&&w("Can't release session options."),o.forEach(s=>t._free(s)),a}}});var V,ae,q,Re,Ne,ie,ue,$e,we=C(()=>{"use strict";V=e=>{switch(e){case"int8":return 3;case"uint8":return 2;case"bool":return 9;case"int16":return 5;case"uint16":return 4;case"int32":return 6;case"uint32":return 12;case"float16":return 10;case"float32":return 1;case"float64":return 11;case"string":return 8;case"int64":return 7;case"uint64":return 13;case"int4":return 22;case"uint4":return 21;default:throw new Error(`unsupported data type: ${e}`)}},ae=e=>{switch(e){case 3:return"int8";case 2:return"uint8";case 9:return"bool";case 5:return"int16";case 4:return"uint16";case 6:return"int32";case 12:return"uint32";case 10:return"float16";case 1:return"float32";case 11:return"float64";case 8:return"string";case 7:return"int64";case 13:return"uint64";case 22:return"int4";case 21:return"uint4";default:throw new Error(`unsupported data type: ${e}`)}},q=(e,t)=>{let n=[-1,4,1,1,2,2,4,8,-1,1,2,8,4,8,-1,-1,-1,-1,-1,-1,-1,.5,.5][e],o=typeof t=="number"?t:t.reduce((r,a)=>r*a,1);return n>0?Math.ceil(o*n):void 0},Re=e=>{switch(e){case"float16":return typeof Float16Array<"u"&&Float16Array.from?Float16Array:Uint16Array;case"float32":return Float32Array;case"uint8":return Uint8Array;case"int8":return Int8Array;case"uint16":return Uint16Array;case"int16":return Int16Array;case"int32":return Int32Array;case"bool":return Uint8Array;case"float64":return Float64Array;case"uint32":return Uint32Array;case"int64":return BigInt64Array;case"uint64":return BigUint64Array;default:throw new Error(`unsupported type: ${e}`)}},Ne=e=>{switch(e){case"verbose":return 0;case"info":return 1;case"warning":return 2;case"error":return 3;case"fatal":return 4;default:throw new Error(`unsupported logging level: ${e}`)}},ie=e=>e==="float32"||e==="float16"||e==="int32"||e==="int64"||e==="uint32"||e==="uint8"||e==="bool"||e==="uint4"||e==="int4",ue=e=>e==="float32"||e==="float16"||e==="int32"||e==="int64"||e==="uint32"||e==="uint64"||e==="int8"||e==="uint8"||e==="bool"||e==="uint4"||e==="int4",$e=e=>{switch(e){case"none":return 0;case"cpu":return 1;case"cpu-pinned":return 2;case"texture":return 3;case"gpu-buffer":return 4;case"ml-tensor":return 5;default:throw new Error(`unsupported data location: ${e}`)}}});var X,ye=C(()=>{"use strict";re();X=async e=>{if(typeof e=="string")if(W)try{let{readFile:t}=de("node:fs/promises");return new Uint8Array(await t(e))}catch(t){if(t.code==="ERR_FS_FILE_TOO_LARGE"){let{createReadStream:n}=de("node:fs"),o=n(e),r=[];for await(let a of o)r.push(a);return new Uint8Array(Buffer.concat(r))}throw t}else{let t=await fetch(e);if(!t.ok)throw new Error(`failed to load external data file: ${e}`);let n=t.headers.get("Content-Length"),o=n?parseInt(n,10):0;if(o<1073741824)return new Uint8Array(await t.arrayBuffer());{if(!t.body)throw new Error(`failed to load external data file: ${e}, no response body.`);let r=t.body.getReader(),a;try{a=new ArrayBuffer(o)}catch(i){if(i instanceof RangeError){let c=Math.ceil(o/65536);a=new WebAssembly.Memory({initial:c,maximum:c}).buffer}else throw i}let s=0;for(;;){let{done:i,value:c}=await r.read();if(i)break;let l=c.byteLength;new Uint8Array(a,s,l).set(c),s+=l}return new Uint8Array(a,0,o)}}else return e instanceof Blob?new Uint8Array(await e.arrayBuffer()):e instanceof Uint8Array?e:new Uint8Array(e)}});var Ct,je,He,J,Dt,Ge,Se,Ve,qe,ze,Je,Ye,Ze=C(()=>{"use strict";ke();Fe();we();H();oe();ye();Ct=(e,t)=>{y()._OrtInit(e,t)!==0&&w("Can't initialize onnxruntime.")},je=async e=>{Ct(e.wasm.numThreads,Ne(e.logLevel))},He=async(e,t)=>{y().asyncInit?.()},J=new Map,Dt=e=>{let t=y(),n=t.stackSave();try{let o=t.PTR_SIZE,r=t.stackAlloc(2*o);t._OrtGetInputOutputCount(e,r,r+o)!==0&&w("Can't get session input/output count.");let s=o===4?"i32":"i64";return[Number(t.getValue(r,s)),Number(t.getValue(r+o,s))]}finally{t.stackRestore(n)}},Ge=(e,t)=>{let n=y(),o=n.stackSave(),r=0;try{let a=n.PTR_SIZE,s=n.stackAlloc(2*a);n._OrtGetInputOutputMetadata(e,t,s,s+a)!==0&&w("Can't get session input/output metadata.");let c=Number(n.getValue(s,"*"));r=Number(n.getValue(s+a,"*"));let l=n.HEAP32[r/4];if(l===0)return[c,0];let f=n.HEAPU32[r/4+1],d=[];for(let u=0;u<f;u++){let p=Number(n.getValue(r+8+u*a,"*"));d.push(p!==0?n.UTF8ToString(p):Number(n.getValue(r+8+(u+f)*a,"*")))}return[c,l,d]}finally{n.stackRestore(o),r!==0&&n._OrtFree(r)}},Se=e=>{let t=y(),n=t._malloc(e.byteLength);if(n===0)throw new Error(`Can't create a session. failed to allocate a buffer of size ${e.byteLength}.`);return t.HEAPU8.set(e,n),[n,e.byteLength]},Ve=async(e,t)=>{let n,o,r=y();Array.isArray(e)?[n,o]=e:e.buffer===r.HEAPU8.buffer?[n,o]=[e.byteOffset,e.byteLength]:[n,o]=Se(e);let a=0,s=0,i=0,c=[],l=[],f=[];try{if([s,c]=await We(t),t?.externalData&&r.mountExternalData){let h=[];for(let E of t.externalData){let v=typeof E=="string"?E:E.path;h.push(X(typeof E=="string"?E:E.data).then(_=>{r.mountExternalData(v,_)}))}await Promise.all(h)}for(let h of t?.executionProviders??[])if((typeof h=="string"?h:h.name)==="webnn"){if(r.shouldTransferToMLTensor=!1,typeof h!="string"){let v=h,_=v?.context,x=v?.gpuDevice,$=v?.deviceType,K=v?.powerPreference;_?r.currentContext=_:x?r.currentContext=await r.webnnCreateMLContext(x):r.currentContext=await r.webnnCreateMLContext({deviceType:$,powerPreference:K})}else r.currentContext=await r.webnnCreateMLContext();break}a=await r._OrtCreateSession(n,o,s),r.webgpuOnCreateSession?.(a),a===0&&w("Can't create a session."),r.jsepOnCreateSession?.(),r.currentContext&&(r.webnnRegisterMLContext(a,r.currentContext),r.currentContext=void 0,r.shouldTransferToMLTensor=!0);let[d,u]=Dt(a),p=!!t?.enableGraphCapture,S=[],O=[],L=[],m=[],g=[];for(let h=0;h<d;h++){let[E,v,_]=Ge(a,h);E===0&&w("Can't get an input name."),l.push(E);let x=r.UTF8ToString(E);S.push(x),L.push(v===0?{name:x,isTensor:!1}:{name:x,isTensor:!0,type:ae(v),shape:_})}for(let h=0;h<u;h++){let[E,v,_]=Ge(a,h+d);E===0&&w("Can't get an output name."),f.push(E);let x=r.UTF8ToString(E);O.push(x),m.push(v===0?{name:x,isTensor:!1}:{name:x,isTensor:!0,type:ae(v),shape:_})}return J.set(a,[a,l,f,null,p,!1]),[a,S,O,L,m]}catch(d){throw l.forEach(u=>r._OrtFree(u)),f.forEach(u=>r._OrtFree(u)),i!==0&&r._OrtReleaseBinding(i)!==0&&w("Can't release IO binding."),a!==0&&r._OrtReleaseSession(a)!==0&&w("Can't release session."),d}finally{r._free(n),s!==0&&r._OrtReleaseSessionOptions(s)!==0&&w("Can't release session options."),c.forEach(d=>r._free(d)),r.unmountExternalData?.()}},qe=e=>{let t=y(),n=J.get(e);if(!n)throw new Error(`cannot release session. invalid session id: ${e}`);let[o,r,a,s,i]=n;s&&(i&&t._OrtClearBoundOutputs(s.handle)!==0&&w("Can't clear bound outputs."),t._OrtReleaseBinding(s.handle)!==0&&w("Can't release IO binding.")),t.jsepOnReleaseSession?.(e),t.webnnOnReleaseSession?.(e),t.webgpuOnReleaseSession?.(e),r.forEach(c=>t._OrtFree(c)),a.forEach(c=>t._OrtFree(c)),t._OrtReleaseSession(o)!==0&&w("Can't release session."),J.delete(e)},ze=async(e,t,n,o,r,a,s=!1)=>{if(!e){t.push(0);return}let i=y(),c=i.PTR_SIZE,l=e[0],f=e[1],d=e[3],u=d,p,S;if(l==="string"&&(d==="gpu-buffer"||d==="ml-tensor"))throw new Error("String tensor is not supported on GPU.");if(s&&d!=="gpu-buffer")throw new Error(`External buffer must be provided for input/output index ${a} when enableGraphCapture is true.`);if(d==="gpu-buffer"){let m=e[2].gpuBuffer;S=q(V(l),f);{let g=i.jsepRegisterBuffer;if(!g)throw new Error('Tensor location "gpu-buffer" is not supported without using WebGPU.');p=g(o,a,m,S)}}else if(d==="ml-tensor"){let m=e[2].mlTensor;S=q(V(l),f);let g=i.webnnRegisterMLTensor;if(!g)throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');p=g(o,m,V(l),f)}else{let m=e[2];if(Array.isArray(m)){S=c*m.length,p=i._malloc(S),n.push(p);for(let g=0;g<m.length;g++){if(typeof m[g]!="string")throw new TypeError(`tensor data at index ${g} is not a string`);i.setValue(p+g*c,A(m[g],n),"*")}}else{let g=i.webnnIsGraphInput;if(l!=="string"&&g){let D=i.UTF8ToString(r);if(g(o,D)){let h=V(l);S=q(h,f),u="ml-tensor";let E=i.webnnCreateTemporaryTensor,v=i.webnnUploadTensor;if(!E||!v)throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');let _=await E(o,h,f);v(_,new Uint8Array(m.buffer,m.byteOffset,m.byteLength)),p=_}else S=m.byteLength,p=i._malloc(S),n.push(p),i.HEAPU8.set(new Uint8Array(m.buffer,m.byteOffset,S),p)}else S=m.byteLength,p=i._malloc(S),n.push(p),i.HEAPU8.set(new Uint8Array(m.buffer,m.byteOffset,S),p)}}let O=i.stackSave(),L=i.stackAlloc(4*f.length);try{f.forEach((g,D)=>i.setValue(L+D*c,g,c===4?"i32":"i64"));let m=i._OrtCreateTensor(V(l),p,S,L,f.length,$e(u));m===0&&w(`Can't create tensor for input/output. session=${o}, index=${a}.`),t.push(m)}finally{i.stackRestore(O)}},Je=async(e,t,n,o,r,a)=>{let s=y(),i=s.PTR_SIZE,c=J.get(e);if(!c)throw new Error(`cannot run inference. invalid session id: ${e}`);let l=c[0],f=c[1],d=c[2],u=c[3],p=c[4],S=c[5],O=t.length,L=o.length,m=0,g=[],D=[],h=[],E=[],v=s.stackSave(),_=s.stackAlloc(O*i),x=s.stackAlloc(O*i),$=s.stackAlloc(L*i),K=s.stackAlloc(L*i);try{[m,g]=De(a);for(let b=0;b<O;b++)await ze(n[b],D,E,e,f[t[b]],t[b],p);for(let b=0;b<L;b++)await ze(r[b],h,E,e,d[o[b]],O+o[b],p);for(let b=0;b<O;b++)s.setValue(_+b*i,D[b],"*"),s.setValue(x+b*i,f[t[b]],"*");for(let b=0;b<L;b++)s.setValue($+b*i,h[b],"*"),s.setValue(K+b*i,d[o[b]],"*");s.jsepOnRunStart?.(l),s.webnnOnRunStart?.(l);let M;M=await s._OrtRun(l,x,_,O,K,L,$,m),M!==0&&w("failed to call OrtRun().");let G=[];for(let b=0;b<L;b++){let z=Number(s.getValue($+b*i,"*"));if(z===h[b]){G.push(r[b]);continue}let ve=s.stackSave(),k=s.stackAlloc(4*i),Y=!1,P,B=0;try{s._OrtGetTensorData(z,k,k+i,k+2*i,k+3*i)!==0&&w(`Can't access output tensor data on index ${b}.`);let le=i===4?"i32":"i64",Q=Number(s.getValue(k,le));B=s.getValue(k+i,"*");let Pe=s.getValue(k+i*2,"*"),mt=Number(s.getValue(k+i*3,le)),R=[];for(let I=0;I<mt;I++)R.push(Number(s.getValue(Pe+I*i,le)));s._OrtFree(Pe)!==0&&w("Can't free memory for tensor dims.");let N=R.reduce((I,T)=>I*T,1);P=ae(Q);let ee=u?.outputPreferredLocations[o[b]];if(P==="string"){if(ee==="gpu-buffer"||ee==="ml-tensor")throw new Error("String tensor is not supported on GPU.");let I=[];for(let T=0;T<N;T++){let j=s.getValue(B+T*i,"*"),te=s.getValue(B+(T+1)*i,"*"),Ie=T===N-1?void 0:te-j;I.push(s.UTF8ToString(j,Ie))}G.push([P,R,I,"cpu"])}else if(ee==="gpu-buffer"&&N>0){let I=s.jsepGetBuffer;if(!I)throw new Error('preferredLocation "gpu-buffer" is not supported without using WebGPU.');let T=I(B),j=q(Q,N);if(j===void 0||!ie(P))throw new Error(`Unsupported data type: ${P}`);Y=!0,G.push([P,R,{gpuBuffer:T,download:s.jsepCreateDownloader(T,j,P),dispose:()=>{s._OrtReleaseTensor(z)!==0&&w("Can't release tensor.")}},"gpu-buffer"])}else if(ee==="ml-tensor"&&N>0){let I=s.webnnEnsureTensor,T=s.webnnIsInt64Supported;if(!I||!T)throw new Error('preferredLocation "ml-tensor" is not supported without using WebNN.');if(q(Q,N)===void 0||!ue(P))throw new Error(`Unsupported data type: ${P}`);if(P==="int64"&&!T(e))throw new Error('preferredLocation "ml-tensor" for int64 output is not supported by current WebNN Context.');let te=await I(e,B,Q,R,!1);Y=!0,G.push([P,R,{mlTensor:te,download:s.webnnCreateMLTensorDownloader(B,P),dispose:()=>{s.webnnReleaseTensorId(B),s._OrtReleaseTensor(z)}},"ml-tensor"])}else{let I=Re(P),T=new I(N);new Uint8Array(T.buffer,T.byteOffset,T.byteLength).set(s.HEAPU8.subarray(B,B+T.byteLength)),G.push([P,R,T,"cpu"])}}finally{s.stackRestore(ve),P==="string"&&B&&s._free(B),Y||s._OrtReleaseTensor(z),s.webnnOnRunEnd?.(l)}}return u&&!p&&(s._OrtClearBoundOutputs(u.handle)!==0&&w("Can't clear bound outputs."),J.set(e,[l,f,d,u,p,!1])),G}finally{s.stackRestore(v),D.forEach(M=>s._OrtReleaseTensor(M)),h.forEach(M=>s._OrtReleaseTensor(M)),E.forEach(M=>s._free(M)),m!==0&&s._OrtReleaseRunOptions(m),g.forEach(M=>s._free(M))}},Ye=e=>{let t=y(),n=J.get(e);if(!n)throw new Error("invalid session id");let o=n[0],r=t._OrtEndProfiling(o);r===0&&w("Can't get an profile file name."),t._OrtFree(r)}});import{env as Ee}from"onnxruntime-common";var he,Xe,Ke,Qe,et,tt,rt,nt,ot,st,Oe=C(()=>{"use strict";Ze();H();me();he=!1,Xe=!1,Ke=!1,Qe=async()=>{if(!Xe){if(he)throw new Error("multiple calls to 'initWasm()' detected.");if(Ke)throw new Error("previous call to 'initWasm()' failed.");he=!0;try{await Ce(Ee.wasm),await je(Ee),Xe=!0}catch(e){throw Ke=!0,e}finally{he=!1}}},et=async e=>{await He(Ee,e)},tt=async e=>Se(e),rt=async(e,t)=>Ve(e,t),nt=async e=>{qe(e)},ot=async(e,t,n,o,r,a)=>Je(e,t,n,o,r,a),st=async e=>{Ye(e)}});import{Tensor as Te,TRACE_FUNC_BEGIN as at,TRACE_FUNC_END as it}from"onnxruntime-common";var ut,Wt,ce,ct=C(()=>{"use strict";Oe();we();re();ye();ut=(e,t)=>{switch(e.location){case"cpu":return[e.type,e.dims,e.data,"cpu"];case"gpu-buffer":return[e.type,e.dims,{gpuBuffer:e.gpuBuffer},"gpu-buffer"];case"ml-tensor":return[e.type,e.dims,{mlTensor:e.mlTensor},"ml-tensor"];default:throw new Error(`invalid data location: ${e.location} for ${t()}`)}},Wt=e=>{switch(e[3]){case"cpu":return new Te(e[0],e[2],e[1]);case"gpu-buffer":{let t=e[0];if(!ie(t))throw new Error(`not supported data type: ${t} for deserializing GPU tensor`);let{gpuBuffer:n,download:o,dispose:r}=e[2];return Te.fromGpuBuffer(n,{dataType:t,dims:e[1],download:o,dispose:r})}case"ml-tensor":{let t=e[0];if(!ue(t))throw new Error(`not supported data type: ${t} for deserializing MLTensor tensor`);let{mlTensor:n,download:o,dispose:r}=e[2];return Te.fromMLTensor(n,{dataType:t,dims:e[1],download:o,dispose:r})}default:throw new Error(`invalid data location: ${e[3]}`)}},ce=class{async fetchModelAndCopyToWasmMemory(t){return tt(await X(t))}async loadModel(t,n){at();let o;typeof t=="string"?W?o=await X(t):o=await this.fetchModelAndCopyToWasmMemory(t):o=t,[this.sessionId,this.inputNames,this.outputNames,this.inputMetadata,this.outputMetadata]=await rt(o,n),it()}async dispose(){return nt(this.sessionId)}async run(t,n,o){at();let r=[],a=[];Object.entries(t).forEach(u=>{let p=u[0],S=u[1],O=this.inputNames.indexOf(p);if(O===-1)throw new Error(`invalid input '${p}'`);r.push(S),a.push(O)});let s=[],i=[];Object.entries(n).forEach(u=>{let p=u[0],S=u[1],O=this.outputNames.indexOf(p);if(O===-1)throw new Error(`invalid output '${p}'`);s.push(S),i.push(O)});let c=r.map((u,p)=>ut(u,()=>`input "${this.inputNames[a[p]]}"`)),l=s.map((u,p)=>u?ut(u,()=>`output "${this.outputNames[i[p]]}"`):null),f=await ot(this.sessionId,a,c,i,l,o),d={};for(let u=0;u<f.length;u++)d[this.outputNames[i[u]]]=s[u]??Wt(f[u]);return it(),d}startProfiling(){}endProfiling(){st(this.sessionId)}}});var lt={};yt(lt,{OnnxruntimeWebAssemblyBackend:()=>fe,initializeFlags:()=>ft,wasmBackend:()=>Ft});import{env as U}from"onnxruntime-common";var ft,fe,Ft,pt=C(()=>{"use strict";Oe();ct();ft=()=>{(typeof U.wasm.initTimeout!="number"||U.wasm.initTimeout<0)&&(U.wasm.initTimeout=0);let e=U.wasm.simd;if(typeof e!="boolean"&&e!==void 0&&e!=="fixed"&&e!=="relaxed"&&(console.warn(`Property "env.wasm.simd" is set to unknown value "${e}". Reset it to \`false\` and ignore SIMD feature checking.`),U.wasm.simd=!1),typeof U.wasm.proxy!="boolean"&&(U.wasm.proxy=!1),typeof U.wasm.trace!="boolean"&&(U.wasm.trace=!1),typeof U.wasm.numThreads!="number"||!Number.isInteger(U.wasm.numThreads)||U.wasm.numThreads<=0)if(typeof self<"u"&&!self.crossOriginIsolated)U.wasm.numThreads=1;else{let t=typeof navigator>"u"?de("node:os").cpus().length:navigator.hardwareConcurrency;U.wasm.numThreads=Math.min(4,Math.ceil((t||1)/2))}},fe=class{async init(t){ft(),await Qe(),await et(t)}async createInferenceSessionHandler(t,n){let o=new ce;return await o.loadModel(t,n),o}},Ft=new fe});export*from"onnxruntime-common";import*as Rt from"onnxruntime-common";import{registerBackend as dt,env as Nt}from"onnxruntime-common";var Le="1.22.0-dev.20250409-89f8206ba4";var Wr=Rt;{let e=(pt(),ht(lt)).wasmBackend;dt("cpu",e,10),dt("wasm",e,10)}Object.defineProperty(Nt.versions,"web",{value:Le,enumerable:!0});export{Wr as default};
//# sourceMappingURL=ort.node.min.mjs.map
