const createChatProvider = (options) => ({
  chat: (model) => Object.assign(options, { model })
});
const createChatProviderWithExtraOptions = (options) => ({
  chat: (model, extraOptions) => Object.assign(options, { model }, extraOptions)
});
const createEmbedProvider = (options) => ({
  embed: (model) => Object.assign(options, { model })
});
const createEmbedProviderWithExtraOptions = (options) => ({
  embed: (model, extraOptions) => Object.assign(options, { model }, extraOptions)
});
const createImageProvider = (options) => ({
  image: (model) => Object.assign(options, { model })
});
const createModelProvider = (options) => ({
  model: () => options
});
const createModelProviderWithExtraOptions = (options) => ({
  model: (extraOptions) => Object.assign(options, extraOptions)
});
const createSpeechProvider = (options) => ({
  speech: (model) => Object.assign(options, { model })
});
const createSpeechProviderWithExtraOptions = (options) => ({
  speech: (model, extraOptions) => Object.assign(options, { model }, extraOptions)
});
const createTranscriptionProvider = (options) => ({
  transcription: (model) => Object.assign(options, { model })
});
const createTranscriptionProviderWithExtraOptions = (options) => ({
  transcription: (model, extraOptions) => Object.assign(options, { model }, extraOptions)
});
const createMetadataProvider = (id, otherMeta) => ({
  metadata: { id, ...otherMeta }
});

const merge = (...arr) => Object.assign(arr[0], ...arr.slice(1));

export { createChatProvider, createChatProviderWithExtraOptions, createEmbedProvider, createEmbedProviderWithExtraOptions, createImageProvider, createMetadataProvider, createModelProvider, createModelProviderWithExtraOptions, createSpeechProvider, createSpeechProviderWithExtraOptions, createTranscriptionProvider, createTranscriptionProviderWithExtraOptions, merge };
