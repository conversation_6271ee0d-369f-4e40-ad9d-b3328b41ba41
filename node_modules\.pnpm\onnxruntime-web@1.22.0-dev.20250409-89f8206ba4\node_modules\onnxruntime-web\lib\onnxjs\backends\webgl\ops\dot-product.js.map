{"version": 3, "file": "dot-product.js", "sourceRoot": "", "sources": ["dot-product.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAGlC,wCAA0C;AAC1C,gDAAyC;AAEzC,oCAAwF;AAExF,6CAAkF;AAClF,qCAA+C;AAE/C,MAAM,+BAA+B,GAAG,CAAC,OAAgB,EAAE,UAAwC,EAAE,EAAE,CAAC,CAAC;IACvG,IAAI,EAAE,gBAAgB;IACtB,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC5D,UAAU,EAAE,OAAO;QACjB,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,mBAAmB,EAAE,mBAAW,CAAC,QAAQ,CAAC;QAC/E,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,mBAAmB,CAAC;IAC3D,QAAQ,EAAE,UAAU,CAAC,kBAAkB;CACxC,CAAC,CAAC;AAEH,MAAM,2BAA2B,GAAG,CAClC,gBAAuC,EACvC,QAAyB,EACzB,MAAyB,EACzB,WAAqB,EACrB,UAAwC,EAC3B,EAAE;IACf,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,mBAAmB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5F,MAAM,WAAW,GAAG,IAAA,4BAAmB,EAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;IACrE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CACvE,mBAAmB,EACnB,mBAAW,CAAC,mBAAmB,CAChC,CAAC;IAEF,MAAM,aAAa,GAAG,gBAAS,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAC5D,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CACjF,WAAW,EACX,mBAAW,CAAC,mBAAmB,CAChC,CAAC;IACF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAEhC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;IACtD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACrE,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,IAAA,iCAAoB,EAAC,UAAU,CAAC,CAAC;IACjF,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,YAAY,GAAG;EACrB,kBAAkB;4BACQ,IAAI;;;;;;;mCAOG,aAAa,CAAC,CAAC,CAAC,kBAAkB,aAAa,CAAC,CAAC,CAAC,kBACjF,aAAa,CAAC,CAAC,CACjB;oCACkC,mBAAmB,CAAC,CAAC,CAAC;kBACxC,SAAS;wBACH,SAAS;uDACsB,WAAW,KAAK,YAAY;uDAC5B,MAAM,KAAK,OAAO;mBACtD,IAAI,CAAC,SAAS,2BAA2B,IAAI,CAAC,SAAS;;;;IAItE,eAAe;;EAEjB,CAAC;IACD,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEK,MAAM,iCAAiC,GAAG,CAC/C,gBAAuC,EACvC,MAAyB,EACzB,WAAqB,EACrB,UAAwC,EACrB,EAAE;IACrB,MAAM,QAAQ,GAAG,+BAA+B,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;IAChF,OAAO;QACL,GAAG,QAAQ;QACX,GAAG,EAAE,GAAG,EAAE,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC;KACpG,CAAC;AACJ,CAAC,CAAC;AAXW,QAAA,iCAAiC,qCAW5C"}