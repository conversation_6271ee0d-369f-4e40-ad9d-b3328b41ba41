'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.RuntimeOptimizations = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const runtime_optimization_record_container_entry_js_1 = require('../../onnxruntime/fbs/runtime-optimization-record-container-entry.js');
class RuntimeOptimizations {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsRuntimeOptimizations(bb, obj) {
    return (obj || new RuntimeOptimizations()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsRuntimeOptimizations(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new RuntimeOptimizations()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  /**
   * mapping from optimizer name to [RuntimeOptimizationRecord]
   */
  records(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset
      ? (obj || new runtime_optimization_record_container_entry_js_1.RuntimeOptimizationRecordContainerEntry()).__init(
          this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4),
          this.bb,
        )
      : null;
  }
  recordsLength() {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startRuntimeOptimizations(builder) {
    builder.startObject(1);
  }
  static addRecords(builder, recordsOffset) {
    builder.addFieldOffset(0, recordsOffset, 0);
  }
  static createRecordsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startRecordsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static endRuntimeOptimizations(builder) {
    const offset = builder.endObject();
    return offset;
  }
  static createRuntimeOptimizations(builder, recordsOffset) {
    RuntimeOptimizations.startRuntimeOptimizations(builder);
    RuntimeOptimizations.addRecords(builder, recordsOffset);
    return RuntimeOptimizations.endRuntimeOptimizations(builder);
  }
}
exports.RuntimeOptimizations = RuntimeOptimizations;
//# sourceMappingURL=runtime-optimizations.js.map
