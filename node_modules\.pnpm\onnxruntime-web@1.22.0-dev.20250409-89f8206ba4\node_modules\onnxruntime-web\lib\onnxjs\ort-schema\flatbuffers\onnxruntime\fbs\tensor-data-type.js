'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, '__esModule', { value: true });
exports.TensorDataType = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
var TensorDataType;
(function (TensorDataType) {
  TensorDataType[(TensorDataType['UNDEFINED'] = 0)] = 'UNDEFINED';
  TensorDataType[(TensorDataType['FLOAT'] = 1)] = 'FLOAT';
  TensorDataType[(TensorDataType['UINT8'] = 2)] = 'UINT8';
  TensorDataType[(TensorDataType['INT8'] = 3)] = 'INT8';
  TensorDataType[(TensorDataType['UINT16'] = 4)] = 'UINT16';
  TensorDataType[(TensorDataType['INT16'] = 5)] = 'INT16';
  TensorDataType[(TensorDataType['INT32'] = 6)] = 'INT32';
  TensorDataType[(TensorDataType['INT64'] = 7)] = 'INT64';
  TensorDataType[(TensorDataType['STRING'] = 8)] = 'STRING';
  TensorDataType[(TensorDataType['BOOL'] = 9)] = 'BOOL';
  TensorDataType[(TensorDataType['FLOAT16'] = 10)] = 'FLOAT16';
  TensorDataType[(TensorDataType['DOUBLE'] = 11)] = 'DOUBLE';
  TensorDataType[(TensorDataType['UINT32'] = 12)] = 'UINT32';
  TensorDataType[(TensorDataType['UINT64'] = 13)] = 'UINT64';
  TensorDataType[(TensorDataType['COMPLEX64'] = 14)] = 'COMPLEX64';
  TensorDataType[(TensorDataType['COMPLEX128'] = 15)] = 'COMPLEX128';
  TensorDataType[(TensorDataType['BFLOAT16'] = 16)] = 'BFLOAT16';
  TensorDataType[(TensorDataType['FLOAT8E4M3FN'] = 17)] = 'FLOAT8E4M3FN';
  TensorDataType[(TensorDataType['FLOAT8E4M3FNUZ'] = 18)] = 'FLOAT8E4M3FNUZ';
  TensorDataType[(TensorDataType['FLOAT8E5M2'] = 19)] = 'FLOAT8E5M2';
  TensorDataType[(TensorDataType['FLOAT8E5M2FNUZ'] = 20)] = 'FLOAT8E5M2FNUZ';
})(TensorDataType || (exports.TensorDataType = TensorDataType = {}));
//# sourceMappingURL=tensor-data-type.js.map
