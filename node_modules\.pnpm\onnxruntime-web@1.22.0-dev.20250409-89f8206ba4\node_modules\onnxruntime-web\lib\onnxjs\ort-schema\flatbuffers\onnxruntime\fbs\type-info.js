'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.TypeInfo = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const type_info_value_js_1 = require('../../onnxruntime/fbs/type-info-value.js');
class TypeInfo {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsTypeInfo(bb, obj) {
    return (obj || new TypeInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsTypeInfo(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new TypeInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  denotation(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  valueType() {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.readUint8(this.bb_pos + offset) : type_info_value_js_1.TypeInfoValue.NONE;
  }
  value(obj) {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
  }
  static startTypeInfo(builder) {
    builder.startObject(3);
  }
  static addDenotation(builder, denotationOffset) {
    builder.addFieldOffset(0, denotationOffset, 0);
  }
  static addValueType(builder, valueType) {
    builder.addFieldInt8(1, valueType, type_info_value_js_1.TypeInfoValue.NONE);
  }
  static addValue(builder, valueOffset) {
    builder.addFieldOffset(2, valueOffset, 0);
  }
  static endTypeInfo(builder) {
    const offset = builder.endObject();
    return offset;
  }
  static createTypeInfo(builder, denotationOffset, valueType, valueOffset) {
    TypeInfo.startTypeInfo(builder);
    TypeInfo.addDenotation(builder, denotationOffset);
    TypeInfo.addValueType(builder, valueType);
    TypeInfo.addValue(builder, valueOffset);
    return TypeInfo.endTypeInfo(builder);
  }
}
exports.TypeInfo = TypeInfo;
//# sourceMappingURL=type-info.js.map
