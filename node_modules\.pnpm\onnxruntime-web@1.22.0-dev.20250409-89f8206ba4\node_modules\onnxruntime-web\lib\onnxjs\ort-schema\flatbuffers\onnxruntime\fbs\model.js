'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.Model = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const graph_js_1 = require('../../onnxruntime/fbs/graph.js');
const operator_set_id_js_1 = require('../../onnxruntime/fbs/operator-set-id.js');
const string_string_entry_js_1 = require('../../onnxruntime/fbs/string-string-entry.js');
class Model {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsModel(bb, obj) {
    return (obj || new Model()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsModel(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new Model()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  irVersion() {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.readInt64(this.bb_pos + offset) : BigInt('0');
  }
  opsetImport(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset
      ? (obj || new operator_set_id_js_1.OperatorSetId()).__init(
          this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4),
          this.bb,
        )
      : null;
  }
  opsetImportLength() {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  producerName(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  producerVersion(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 10);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  domain(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 12);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  modelVersion() {
    const offset = this.bb.__offset(this.bb_pos, 14);
    return offset ? this.bb.readInt64(this.bb_pos + offset) : BigInt('0');
  }
  docString(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 16);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  graph(obj) {
    const offset = this.bb.__offset(this.bb_pos, 18);
    return offset ? (obj || new graph_js_1.Graph()).__init(this.bb.__indirect(this.bb_pos + offset), this.bb) : null;
  }
  graphDocString(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 20);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  metadataProps(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 22);
    return offset
      ? (obj || new string_string_entry_js_1.StringStringEntry()).__init(
          this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4),
          this.bb,
        )
      : null;
  }
  metadataPropsLength() {
    const offset = this.bb.__offset(this.bb_pos, 22);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startModel(builder) {
    builder.startObject(10);
  }
  static addIrVersion(builder, irVersion) {
    builder.addFieldInt64(0, irVersion, BigInt('0'));
  }
  static addOpsetImport(builder, opsetImportOffset) {
    builder.addFieldOffset(1, opsetImportOffset, 0);
  }
  static createOpsetImportVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startOpsetImportVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static addProducerName(builder, producerNameOffset) {
    builder.addFieldOffset(2, producerNameOffset, 0);
  }
  static addProducerVersion(builder, producerVersionOffset) {
    builder.addFieldOffset(3, producerVersionOffset, 0);
  }
  static addDomain(builder, domainOffset) {
    builder.addFieldOffset(4, domainOffset, 0);
  }
  static addModelVersion(builder, modelVersion) {
    builder.addFieldInt64(5, modelVersion, BigInt('0'));
  }
  static addDocString(builder, docStringOffset) {
    builder.addFieldOffset(6, docStringOffset, 0);
  }
  static addGraph(builder, graphOffset) {
    builder.addFieldOffset(7, graphOffset, 0);
  }
  static addGraphDocString(builder, graphDocStringOffset) {
    builder.addFieldOffset(8, graphDocStringOffset, 0);
  }
  static addMetadataProps(builder, metadataPropsOffset) {
    builder.addFieldOffset(9, metadataPropsOffset, 0);
  }
  static createMetadataPropsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startMetadataPropsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static endModel(builder) {
    const offset = builder.endObject();
    return offset;
  }
}
exports.Model = Model;
//# sourceMappingURL=model.js.map
