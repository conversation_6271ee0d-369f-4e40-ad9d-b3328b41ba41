{"name": "@vueuse/core", "type": "module", "version": "13.6.0", "description": "Collection of essential Vue Composition Utilities", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use", "utils"], "sideEffects": false, "exports": {".": "./index.mjs", "./*": "./*", "./metadata": "./metadata.mjs"}, "main": "./index.mjs", "module": "./index.mjs", "unpkg": "./index.iife.min.js", "jsdelivr": "./index.iife.min.js", "types": "./index.d.mts", "files": ["*.d.mts", "*.js", "*.mjs"], "peerDependencies": {"vue": "^3.5.0"}, "dependencies": {"@types/web-bluetooth": "^0.0.21", "@vueuse/metadata": "13.6.0", "@vueuse/shared": "13.6.0"}, "scripts": {"build": "rollup --config=rollup.config.ts --configPlugin=rollup-plugin-esbuild", "test:attw": "attw --pack --config-path ../../.attw.json ."}}