const alphabet$1 = {
  Base64: new TextEncoder().encode("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),
  Base64Url: new TextEncoder().encode("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_")
};
const rAlphabet$1 = {
  Base64: Uint8Array.from(Array.from({ length: 128 }).fill(64)),
  // alphabet.Base64.length
  Base64Url: Uint8Array.from(Array.from({ length: 128 }).fill(64))
};
alphabet$1.Base64.forEach((byte, i) => rAlphabet$1.Base64[byte] = i);
alphabet$1.Base64Url.forEach((byte, i) => rAlphabet$1.Base64Url[byte] = i);
const calcSizeBase64 = (originalSize) => {
  return ((originalSize + 2) / 3 | 0) * 4;
};
const encode = (buffer, i, o, alphabet2, padding2) => {
  i += 2;
  for (; i < buffer.length; i += 3) {
    const x = buffer[i - 2] << 16 | buffer[i - 1] << 8 | buffer[i];
    buffer[o++] = alphabet2[x >> 18];
    buffer[o++] = alphabet2[x >> 12 & 63];
    buffer[o++] = alphabet2[x >> 6 & 63];
    buffer[o++] = alphabet2[x & 63];
  }
  switch (i) {
    case buffer.length: {
      const x = buffer[i - 2] << 16 | buffer[i - 1] << 8;
      buffer[o++] = alphabet2[x >> 18];
      buffer[o++] = alphabet2[x >> 12 & 63];
      buffer[o++] = alphabet2[x >> 6 & 63];
      buffer[o++] = padding2;
      break;
    }
    case buffer.length + 1: {
      const x = buffer[i - 2] << 16;
      buffer[o++] = alphabet2[x >> 18];
      buffer[o++] = alphabet2[x >> 12 & 63];
      buffer[o++] = padding2;
      buffer[o++] = padding2;
      break;
    }
  }
  return o;
};
const getByte = (char, alphabet2) => {
  const byte = alphabet2[char] ?? 64;
  if (byte === 64) {
    throw new TypeError(
      `Cannot decode input as base64: Invalid character (${String.fromCharCode(char)})`
    );
  }
  return byte;
};
const decode = (buffer, i, o, alphabet2, padding2) => {
  for (let x = buffer.length - 2; x < buffer.length; ++x) {
    if (buffer[x] === padding2) {
      for (let y = x + 1; y < buffer.length; ++y) {
        if (buffer[y] !== padding2) {
          throw new TypeError(
            `Cannot decode input as base64: Invalid character (${String.fromCharCode(buffer[y])})`
          );
        }
      }
      buffer = buffer.subarray(0, x);
      break;
    }
  }
  if ((buffer.length - o) % 4 === 1) {
    throw new RangeError(
      `Cannot decode input as base64: Length (${buffer.length - o}), excluding padding, must not have a remainder of 1 when divided by 4`
    );
  }
  i += 3;
  for (; i < buffer.length; i += 4) {
    const x = getByte(buffer[i - 3], alphabet2) << 18 | getByte(buffer[i - 2], alphabet2) << 12 | getByte(buffer[i - 1], alphabet2) << 6 | getByte(buffer[i], alphabet2);
    buffer[o++] = x >> 16;
    buffer[o++] = x >> 8 & 255;
    buffer[o++] = x & 255;
  }
  switch (i) {
    case buffer.length: {
      const x = getByte(buffer[i - 3], alphabet2) << 18 | getByte(buffer[i - 2], alphabet2) << 12 | getByte(buffer[i - 1], alphabet2) << 6;
      buffer[o++] = x >> 16;
      buffer[o++] = x >> 8 & 255;
      break;
    }
    case buffer.length + 1: {
      const x = getByte(buffer[i - 3], alphabet2) << 18 | getByte(buffer[i - 2], alphabet2) << 12;
      buffer[o++] = x >> 16;
      break;
    }
  }
  return o;
};

const detach = (buffer, maxSize) => {
  const originalSize = buffer.length;
  if (buffer.byteOffset) {
    const b = Uint8Array.from(buffer.buffer);
    b.set(buffer);
    buffer = b.subarray(0, originalSize);
  }
  buffer = Uint8Array.from(buffer.buffer.transfer(maxSize));
  buffer.set(buffer.subarray(0, originalSize), maxSize - originalSize);
  return [buffer, maxSize - originalSize];
};

const padding = "=".charCodeAt(0);
const alphabet = new TextEncoder().encode("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/");
const rAlphabet = Uint8Array.from(Array.from({ length: 128 }).fill(64));
alphabet.forEach((byte, i) => rAlphabet[byte] = i);
const encodeBase64 = (data) => {
  if (typeof data === "string") {
    data = new TextEncoder().encode(data);
  } else if (data instanceof ArrayBuffer) {
    data = Uint8Array.from(data).slice();
  } else {
    data = data.slice();
  }
  const [output, i] = detach(
    data,
    calcSizeBase64(data.length)
  );
  encode(output, i, 0, alphabet, padding);
  return new TextDecoder().decode(output);
};
const decodeBase64 = (b64) => {
  const output = new TextEncoder().encode(b64);
  return Uint8Array.from(output.buffer.transfer(decode(output, 0, 0, rAlphabet, padding)));
};

export { decodeBase64, encodeBase64 };
