{"version": 3, "file": "texture-layout-strategy.js", "sourceRoot": "", "sources": ["texture-layout-strategy.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,iDAA0C;AAC1C,qCAAoC;AAgBpC;;GAEG;AACH,MAAa,8BAA8B;IACzC,YAAmB,cAAsB;QAAtB,mBAAc,GAAd,cAAc,CAAQ;IAAG,CAAC;IAC7C,gBAAgB,CAAC,KAAwB,EAAE,KAAwB;QACjE,gBAAgB;QAChB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACf;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE;YAC1C,2BAA2B;YAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACzG,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjG,IAAI,KAAK,GAAG,cAAc,IAAI,KAAK,GAAG,cAAc,EAAE;gBACpD,qBAAqB;gBACrB,+BAA+B;gBAC/B,mBAAM,CAAC,OAAO,CACZ,eAAe,EACf,2DAA2D,KAAK,eAAe,KAAK,CAAC,SAAS,EAAE,CACjG,CAAC;aACH;iBAAM;gBACL,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACvB;SACF;QACD,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEhD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAE7C,OAAO,KAAK,GAAG,cAAc,IAAI,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,EAAE;YAC3D,IAAI,SAAS,GAAG,KAAK,KAAK,CAAC,EAAE;gBAC3B,MAAM;aACP;SACF;QAED,IAAI,KAAK,IAAI,cAAc,IAAI,SAAS,GAAG,KAAK,KAAK,CAAC,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,2DAA2D,KAAK,EAAE,CAAC,CAAC;SACrF;QACD,OAAO,CAAC,KAAK,EAAE,SAAS,GAAG,KAAK,CAAC,CAAC;IACpC,CAAC;CACF;AAtCD,wEAsCC;AAED,MAAa,qBAAqB;IAChC,YAAmB,cAAsB;QAAtB,mBAAc,GAAd,cAAc,CAAQ;IAAG,CAAC;IAC7C,gBAAgB,CAAC,KAAwB,EAAE,KAAwB;QACjE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC7C,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE;YAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACX,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACZ;QACD,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE;YAC5B,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACvB;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,cAAc,CAAC,KAAwB,EAAE,KAAwB;QAC/D,MAAM,QAAQ,GAAG,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC;QACzC,gBAAgB;QAChB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACnC;QACD,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzC,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE;YAC1C,2BAA2B;YAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACzG,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjG,IAAI,KAAK,GAAG,cAAc,IAAI,KAAK,GAAG,cAAc,EAAE;gBACpD,qBAAqB;gBACrB,+BAA+B;gBAC/B,mBAAM,CAAC,OAAO,CACZ,eAAe,EACf,2DAA2D,KAAK,eAAe,KAAK,CAAC,SAAS,EAAE,CACjG,CAAC;aACH;iBAAM;gBACL,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACvB;SACF;QACD,IAAI,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,EAAE;YACZ,cAAc,GAAG,cAAc,GAAG,CAAC,CAAC;YAEpC,4EAA4E;YAC5E,0EAA0E;YAC1E,2EAA2E;YAC3E,0EAA0E;YAC1E,gEAAgE;YAChE,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAChC,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CACjG,CAAC;YAEF,sEAAsE;YACtE,UAAU;YACV,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,QAAQ,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,4EAA4E;QAC5E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,MAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC7C,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;SACnC;QAED,MAAM,IAAI,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,cAAc,EAAE;YAClD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAClB;aAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,EAAE;YAClG,OAAO,QAA4B,CAAC;SACrC;aAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,EAAE;YAChH,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;aAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,EAAE;YAChH,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;aAAM,IACL,QAAQ,CAAC,MAAM,KAAK,CAAC;YACrB,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc;YACzD,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,EAC7B;YACA,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D;aAAM,IACL,QAAQ,CAAC,MAAM,KAAK,CAAC;YACrB,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc;YAC7B,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,EACzD;YACA,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D;aAAM;YACL,IAAI,QAAQ,EAAE;gBACZ,qEAAqE;gBACrE,uEAAuE;gBACvE,qEAAqE;gBACrE,mEAAmE;gBACnE,+BAA+B;gBAC/B,OAAO,mBAAmB,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAqB,CAAC;aAC5E;YACD,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;SAClC;IACH,CAAC;CACF;AA/FD,sDA+FC;AAED,SAAgB,YAAY,CAAC,KAAe,EAAE,IAAe;IAC3D,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,MAAM,YAAY,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IAC9E,MAAM,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IACtF,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACrC,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;aACjF;YACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAClB;YACD,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBAChB,CAAC,EAAE,CAAC;aACL;SACF;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAClB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;KACF;IACD,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAChC,CAAC;AAzBD,oCAyBC;AAED,SAAgB,cAAc,CAAC,IAAuB,EAAE,KAAe;IACrE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAE1B,kBAAkB;IAClB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,EAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE9E,wBAAwB;IACxB,IAAA,aAAM,EACJ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,EAC5C,GAAG,EAAE,CAAC,+CAA+C,IAAI,KAAK,IAAI,QAAQ,GAAG,YAAY,IAAI,EAAE,CAChG,CAAC;IAEF,0BAA0B;IAC1B,IAAA,aAAM,EAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,gDAAgD,GAAG,YAAY,IAAI,EAAE,CAAC,CAAC;IAEvG,wBAAwB;IACxB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC;AAjBD,wCAiBC;AACD,SAAgB,KAAK,CAAC,CAAS;IAC7B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC;AAFD,sBAEC;AACD,SAAgB,aAAa,CAAC,KAAe;IAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,UAAU;QACV,OAAO,CAAC,CAAC;KACV;IACD,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;KAClB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAVD,sCAUC;AACD,SAAgB,WAAW,CAAC,KAAe;IACzC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACrE;IAED,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC;AAND,kCAMC;AACD,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAC1C,CAAC;AAHD,kDAGC;AACD,SAAgB,WAAW,CAAC,KAAe,EAAE,UAAU,GAAG,CAAC;IACzD,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC;AAClE,CAAC;AAFD,kCAEC"}