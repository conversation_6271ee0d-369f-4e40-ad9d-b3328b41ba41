{"version": 3, "file": "depth-to-space.js", "sourceRoot": "", "sources": ["depth-to-space.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAOlC,2CAA6D;AAOtD,MAAM,YAAY,GAAmD,CAC1E,gBAAuC,EACvC,MAAgB,EAChB,UAAkC,EACxB,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IACvB,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;IACvC,MAAM,YAAY,GAAG,SAAS,GAAG,SAAS,CAAC;IAC3C,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1F,MAAM,iBAAiB,GACrB,UAAU,CAAC,IAAI,KAAK,KAAK;QACvB,CAAC,CAAC;YACE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACjB,SAAS;YACT,SAAS;YACT,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;YAChC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACjB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SAClB;QACH,CAAC,CAAC;YACE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACjB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;YAChC,SAAS;YACT,SAAS;YACT,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACjB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SAClB,CAAC;IAER,0CAA0C;IAC1C,+CAA+C;IAC/C,iDAAiD;IACjD,oCAAoC;IAEpC,gBAAgB;IAChB,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAE3F,YAAY;IACZ,MAAM,mBAAmB,GAAwB,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,aAAa,EAAE,EAAE,CAAC;IACvG,MAAM,CAAC,eAAe,CAAC,GAAG,IAAA,qBAAS,EAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAC,CAAC;IAElG,iBAAiB;IACjB,MAAM,kBAAkB,GAAG;QACzB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACjB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;QAChC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS;QAC7B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS;KAC9B,CAAC;IACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,eAAe,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;IACrF,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAjDW,QAAA,YAAY,gBAiDvB;AAEK,MAAM,2BAA2B,GAAmD,CACzF,IAAgB,EACQ,EAAE;IAC1B,6BAA6B;IAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACtD,IAAI,SAAS,GAAG,CAAC,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,qCAAqC,SAAS,mBAAmB,CAAC,CAAC;KACpF;IACD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACtD,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,mBAAmB,CAAC,CAAC;KAChE;IACD,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC7B,CAAC,CAAC;AAbW,QAAA,2BAA2B,+BAatC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;KAC3E;IAED,+BAA+B;IAC/B,uCAAuC;IACvC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9D,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;KAC1E;AACH,CAAC,CAAC"}