'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, '__esModule', { value: true });
exports.NodeType = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
var NodeType;
(function (NodeType) {
  NodeType[(NodeType['Primitive'] = 0)] = 'Primitive';
  NodeType[(NodeType['Fused'] = 1)] = 'Fused';
})(NodeType || (exports.NodeType = NodeType = {}));
//# sourceMappingURL=node-type.js.map
