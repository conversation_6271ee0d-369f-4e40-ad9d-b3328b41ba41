@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vite@6.3.5_@types+node@24.3.0\node_modules\vite\bin\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vite@6.3.5_@types+node@24.3.0\node_modules\vite\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vite@6.3.5_@types+node@24.3.0\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vite@6.3.5_@types+node@24.3.0\node_modules\vite\bin\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vite@6.3.5_@types+node@24.3.0\node_modules\vite\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vite@6.3.5_@types+node@24.3.0\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\vite@6.3.5_@types+node@24.3.0\node_modules\vite\bin\vite.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\vite@6.3.5_@types+node@24.3.0\node_modules\vite\bin\vite.js" %*
)
