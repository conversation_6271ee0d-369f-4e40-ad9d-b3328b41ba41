{"version": 3, "file": "runtime-optimizations.js", "sourceRoot": "", "sources": ["runtime-optimizations.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;AAErE,oIAAoI;AAEpI,yDAA2C;AAE3C,yIAA+H;AAE/H,MAAa,oBAAoB;IAAjC;QACE,OAAE,GAAkC,IAAI,CAAC;QACzC,WAAM,GAAG,CAAC,CAAC;IAyEb,CAAC;IAxEC,MAAM,CAAC,CAAS,EAAE,EAA0B;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,6BAA6B,CAAC,EAA0B,EAAE,GAA0B;QACzF,OAAO,CAAC,GAAG,IAAI,IAAI,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IAED,MAAM,CAAC,yCAAyC,CAC9C,EAA0B,EAC1B,GAA0B;QAE1B,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IAED;;OAEG;IACH,OAAO,CACL,KAAa,EACb,GAA6C;QAE7C,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM;YACX,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,wFAAuC,EAAE,CAAC,CAAC,MAAM,CAC3D,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,EACxE,IAAI,CAAC,EAAG,CACT;YACH,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED,aAAa;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,OAA4B;QAC3D,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAA4B,EAAE,aAAiC;QAC/E,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,OAA4B,EAAE,IAA0B;QACjF,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;SAC7B;QACD,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAA4B,EAAE,QAAgB;QACtE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAA4B;QACzD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,0BAA0B,CAC/B,OAA4B,EAC5B,aAAiC;QAEjC,oBAAoB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACxD,oBAAoB,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QACxD,OAAO,oBAAoB,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;IAC/D,CAAC;CACF;AA3ED,oDA2EC"}