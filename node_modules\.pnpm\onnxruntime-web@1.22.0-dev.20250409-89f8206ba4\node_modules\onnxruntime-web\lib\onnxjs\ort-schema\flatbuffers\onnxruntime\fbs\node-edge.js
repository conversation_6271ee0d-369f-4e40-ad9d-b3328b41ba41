'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.NodeEdge = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const edge_end_js_1 = require('../../onnxruntime/fbs/edge-end.js');
class NodeEdge {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsNodeEdge(bb, obj) {
    return (obj || new NodeEdge()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsNodeEdge(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new NodeEdge()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  nodeIndex() {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
  }
  inputEdges(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset
      ? (obj || new edge_end_js_1.EdgeEnd()).__init(this.bb.__vector(this.bb_pos + offset) + index * 12, this.bb)
      : null;
  }
  inputEdgesLength() {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  outputEdges(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset
      ? (obj || new edge_end_js_1.EdgeEnd()).__init(this.bb.__vector(this.bb_pos + offset) + index * 12, this.bb)
      : null;
  }
  outputEdgesLength() {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startNodeEdge(builder) {
    builder.startObject(3);
  }
  static addNodeIndex(builder, nodeIndex) {
    builder.addFieldInt32(0, nodeIndex, 0);
  }
  static addInputEdges(builder, inputEdgesOffset) {
    builder.addFieldOffset(1, inputEdgesOffset, 0);
  }
  static startInputEdgesVector(builder, numElems) {
    builder.startVector(12, numElems, 4);
  }
  static addOutputEdges(builder, outputEdgesOffset) {
    builder.addFieldOffset(2, outputEdgesOffset, 0);
  }
  static startOutputEdgesVector(builder, numElems) {
    builder.startVector(12, numElems, 4);
  }
  static endNodeEdge(builder) {
    const offset = builder.endObject();
    return offset;
  }
  static createNodeEdge(builder, nodeIndex, inputEdgesOffset, outputEdgesOffset) {
    NodeEdge.startNodeEdge(builder);
    NodeEdge.addNodeIndex(builder, nodeIndex);
    NodeEdge.addInputEdges(builder, inputEdgesOffset);
    NodeEdge.addOutputEdges(builder, outputEdgesOffset);
    return NodeEdge.endNodeEdge(builder);
  }
}
exports.NodeEdge = NodeEdge;
//# sourceMappingURL=node-edge.js.map
