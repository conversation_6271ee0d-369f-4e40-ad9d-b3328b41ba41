'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.SparseTensor = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const tensor_js_1 = require('../../onnxruntime/fbs/tensor.js');
class SparseTensor {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsSparseTensor(bb, obj) {
    return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsSparseTensor(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  values(obj) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? (obj || new tensor_js_1.Tensor()).__init(this.bb.__indirect(this.bb_pos + offset), this.bb) : null;
  }
  indices(obj) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? (obj || new tensor_js_1.Tensor()).__init(this.bb.__indirect(this.bb_pos + offset), this.bb) : null;
  }
  dims(index) {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.readInt64(this.bb.__vector(this.bb_pos + offset) + index * 8) : BigInt(0);
  }
  dimsLength() {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startSparseTensor(builder) {
    builder.startObject(3);
  }
  static addValues(builder, valuesOffset) {
    builder.addFieldOffset(0, valuesOffset, 0);
  }
  static addIndices(builder, indicesOffset) {
    builder.addFieldOffset(1, indicesOffset, 0);
  }
  static addDims(builder, dimsOffset) {
    builder.addFieldOffset(2, dimsOffset, 0);
  }
  static createDimsVector(builder, data) {
    builder.startVector(8, data.length, 8);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt64(data[i]);
    }
    return builder.endVector();
  }
  static startDimsVector(builder, numElems) {
    builder.startVector(8, numElems, 8);
  }
  static endSparseTensor(builder) {
    const offset = builder.endObject();
    return offset;
  }
}
exports.SparseTensor = SparseTensor;
//# sourceMappingURL=sparse-tensor.js.map
