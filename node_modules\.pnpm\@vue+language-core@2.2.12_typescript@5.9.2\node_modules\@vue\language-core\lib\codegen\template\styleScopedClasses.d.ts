import * as CompilerDOM from '@vue/compiler-dom';
import type { Code } from '../../types';
import type { TemplateCodegenContext } from './context';
import type { TemplateCodegenOptions } from './index';
export declare function generateStyleScopedClassReferences(ctx: TemplateCodegenContext, withDot?: boolean): Generator<Code>;
export declare function collectStyleScopedClassReferences(options: TemplateCodegenOptions, ctx: TemplateCodegenContext, node: CompilerDOM.ElementNode): void;
