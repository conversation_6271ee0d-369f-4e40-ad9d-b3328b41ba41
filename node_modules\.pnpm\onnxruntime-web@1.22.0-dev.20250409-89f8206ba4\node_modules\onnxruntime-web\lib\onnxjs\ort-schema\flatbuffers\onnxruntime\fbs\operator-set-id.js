'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.OperatorSetId = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
class OperatorSetId {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsOperatorSetId(bb, obj) {
    return (obj || new OperatorSetId()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsOperatorSetId(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new OperatorSetId()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  domain(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  version() {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.readInt64(this.bb_pos + offset) : BigInt('0');
  }
  static startOperatorSetId(builder) {
    builder.startObject(2);
  }
  static addDomain(builder, domainOffset) {
    builder.addFieldOffset(0, domainOffset, 0);
  }
  static addVersion(builder, version) {
    builder.addFieldInt64(1, version, BigInt('0'));
  }
  static endOperatorSetId(builder) {
    const offset = builder.endObject();
    return offset;
  }
  static createOperatorSetId(builder, domainOffset, version) {
    OperatorSetId.startOperatorSetId(builder);
    OperatorSetId.addDomain(builder, domainOffset);
    OperatorSetId.addVersion(builder, version);
    return OperatorSetId.endOperatorSetId(builder);
  }
}
exports.OperatorSetId = OperatorSetId;
//# sourceMappingURL=operator-set-id.js.map
