'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.RuntimeOptimizationRecord = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const nodes_to_optimize_indices_js_1 = require('../../onnxruntime/fbs/nodes-to-optimize-indices.js');
/**
 * a single runtime optimization
 * see corresponding type in onnxruntime/core/graph/runtime_optimization_record.h
 */
class RuntimeOptimizationRecord {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsRuntimeOptimizationRecord(bb, obj) {
    return (obj || new RuntimeOptimizationRecord()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsRuntimeOptimizationRecord(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new RuntimeOptimizationRecord()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  actionId(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  nodesToOptimizeIndices(obj) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset
      ? (obj || new nodes_to_optimize_indices_js_1.NodesToOptimizeIndices()).__init(
          this.bb.__indirect(this.bb_pos + offset),
          this.bb,
        )
      : null;
  }
  producedOpIds(index, optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 10);
    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
  }
  producedOpIdsLength() {
    const offset = this.bb.__offset(this.bb_pos, 10);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startRuntimeOptimizationRecord(builder) {
    builder.startObject(4);
  }
  static addActionId(builder, actionIdOffset) {
    builder.addFieldOffset(0, actionIdOffset, 0);
  }
  static addNodesToOptimizeIndices(builder, nodesToOptimizeIndicesOffset) {
    builder.addFieldOffset(1, nodesToOptimizeIndicesOffset, 0);
  }
  static addProducedOpIds(builder, producedOpIdsOffset) {
    builder.addFieldOffset(3, producedOpIdsOffset, 0);
  }
  static createProducedOpIdsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startProducedOpIdsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static endRuntimeOptimizationRecord(builder) {
    const offset = builder.endObject();
    return offset;
  }
}
exports.RuntimeOptimizationRecord = RuntimeOptimizationRecord;
//# sourceMappingURL=runtime-optimization-record.js.map
