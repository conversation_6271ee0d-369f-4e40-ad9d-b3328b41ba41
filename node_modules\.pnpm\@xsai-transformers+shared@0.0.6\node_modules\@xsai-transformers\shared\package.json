{"name": "@xsai-transformers/shared", "type": "module", "version": "0.0.6", "description": "Transformers.js wrapper makes it easy to use with xsai", "author": {"name": "Moeru AI", "email": "<EMAIL>", "url": "https://github.com/moeru-ai"}, "license": "MIT", "homepage": "https://github.com/moeru-ai/xsai-transformers", "repository": {"type": "git", "url": "git+https://github.com/moeru-ai/xsai-transformers.git", "directory": "packages/shared"}, "bugs": "https://github.com/moeru-ai/xsai-transformers/issues", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./base64": {"types": "./dist/base64/index.d.ts", "default": "./dist/base64/index.js"}, "./worker": {"types": "./dist/worker/index.d.ts", "default": "./dist/worker/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "./package.json": "./package.json"}, "files": ["README.md", "dist", "package.json"], "dependencies": {"@huggingface/transformers": "^3.5.1", "@xsai-ext/shared-providers": "^0.2.2", "@xsai/shared": "^0.2.2", "defu": "^6.1.4", "onnxruntime-common": "^1.22.0"}, "scripts": {"build": "pkgroll", "typecheck": "tsc --noEmit"}, "main": "./dist/index.js", "types": "./dist/index.d.ts"}