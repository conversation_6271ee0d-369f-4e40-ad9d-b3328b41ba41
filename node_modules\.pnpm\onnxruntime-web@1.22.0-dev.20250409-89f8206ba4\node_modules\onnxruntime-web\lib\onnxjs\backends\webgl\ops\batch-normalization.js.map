{"version": 3, "file": "batch-normalization.js", "sourceRoot": "", "sources": ["batch-normalization.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAIvG,gDAAyC;AAEzC,oCAAoD;AAQpD,MAAM,iCAAiC,GAAG;IACxC,IAAI,EAAE,oBAAoB;IAC1B,UAAU,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;IACnD,UAAU,EAAE;QACV,mBAAW,CAAC,QAAQ;QACpB,mBAAW,CAAC,QAAQ;QACpB,mBAAW,CAAC,QAAQ;QACpB,mBAAW,CAAC,QAAQ;QACpB,mBAAW,CAAC,QAAQ;KACrB;CACF,CAAC;AAEK,MAAM,kBAAkB,GAAyD,CACtF,gBAAuC,EACvC,MAAgB,EAChB,UAAwC,EAC9B,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IACvB,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CACjC;QACE,GAAG,iCAAiC;QACpC,SAAS,EAAE,UAAU,CAAC,QAAQ;QAC9B,GAAG,EAAE,GAAG,EAAE,CAAC,mCAAmC,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC;KACrF,EACD,MAAM,CACP,CAAC;IACF,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAfW,QAAA,kBAAkB,sBAe7B;AAEK,MAAM,iCAAiC,GAAyD,CACrG,IAAgB,EACc,EAAE;IAChC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACrD,OAAO,IAAA,sDAA2B,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;AACrE,CAAC,CAAC;AAPW,QAAA,iCAAiC,qCAO5C;AAEF,MAAM,mCAAmC,GAAG,CAC1C,gBAAuC,EACvC,MAAgB,EAChB,UAAwC,EAC3B,EAAE;IACf,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACnC,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CAC/E,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EACd,mBAAW,CAAC,QAAQ,CACrB,CAAC;IACF,MAAM,YAAY,GAAG;sBACD,IAAI;iDACuB,UAAU,KAAK,WAAW;oCACvC,IAAI,CAAC,SAAS;mCACf,IAAI,CAAC,SAAS;uCACV,IAAI,CAAC,SAAS;gCACrB,IAAI,CAAC,SAAS;;oEAEsB,UAAU,CAAC,OAAO;IAClF,CAAC;IACH,OAAO;QACL,GAAG,iCAAiC;QACpC,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACzF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;KAC1D;IAED,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEvB,iEAAiE;IACjE,4CAA4C;IAC5C,IACE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;QACjB,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;QACvB,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EACtB;QACA,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,IACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAC1B;QACA,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,IACE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC;QAC9C,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;QACtD,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC;QAC9C,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC;QACpD,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,EACpD;QACA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;AACH,CAAC,CAAC"}