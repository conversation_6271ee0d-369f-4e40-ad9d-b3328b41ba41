#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\he@1.2.0\node_modules\he\bin\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\he@1.2.0\node_modules\he\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\he@1.2.0\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/he@1.2.0/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../bin/he" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../bin/he" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../bin/he" $args
  } else {
    & "node$exe"  "$basedir/../../bin/he" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
