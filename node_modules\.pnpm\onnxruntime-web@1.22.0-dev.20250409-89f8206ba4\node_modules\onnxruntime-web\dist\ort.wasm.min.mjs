/*!
 * ONNX Runtime Web v1.22.0-dev.20250409-89f8206ba4
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var He=Object.defineProperty;var Sn=Object.getOwnPropertyDescriptor;var An=Object.getOwnPropertyNames;var On=Object.prototype.hasOwnProperty;var je=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var E=(e,t)=>()=>(e&&(t=e(e=0)),t);var Ve=(e,t)=>{for(var n in t)He(e,n,{get:t[n],enumerable:!0})},In=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of An(t))!On.call(e,r)&&r!==n&&He(e,r,{get:()=>t[r],enumerable:!(o=Sn(t,r))||o.enumerable});return e};var dt=e=>In(He({},"__esModule",{value:!0}),e);var be,H,ue,Pn,lt,Ye=E(()=>{"use strict";be=new Map,H=[],ue=(e,t,n)=>{if(t&&typeof t.init=="function"&&typeof t.createInferenceSessionHandler=="function"){let o=be.get(e);if(o===void 0)be.set(e,{backend:t,priority:n});else{if(o.priority>n)return;if(o.priority===n&&o.backend!==t)throw new Error(`cannot register backend "${e}" using priority ${n}`)}if(n>=0){let r=H.indexOf(e);r!==-1&&H.splice(r,1);for(let a=0;a<H.length;a++)if(be.get(H[a]).priority<=n){H.splice(a,0,e);return}H.push(e)}return}throw new TypeError("not a valid backend")},Pn=async e=>{let t=be.get(e);if(!t)return"backend not found.";if(t.initialized)return t.backend;if(t.aborted)return t.error;{let n=!!t.initPromise;try{return n||(t.initPromise=t.backend.init(e)),await t.initPromise,t.initialized=!0,t.backend}catch(o){return n||(t.error=`${o}`,t.aborted=!0),t.error}finally{delete t.initPromise}}},lt=async e=>{let t=e.executionProviders||[],n=t.map(u=>typeof u=="string"?u:u.name),o=n.length===0?H:n,r,a=[],i=new Set;for(let u of o){let f=await Pn(u);typeof f=="string"?a.push({name:u,err:f}):(r||(r=f),r===f&&i.add(u))}if(!r)throw new Error(`no available backend found. ERR: ${a.map(u=>`[${u.name}] ${u.err}`).join(", ")}`);for(let{name:u,err:f}of a)n.includes(u)&&console.warn(`removing requested execution provider "${u}" from session options because it is not available: ${f}`);let s=t.filter(u=>i.has(typeof u=="string"?u:u.name));return[r,new Proxy(e,{get:(u,f)=>f==="executionProviders"?s:Reflect.get(u,f)})]}});var pt=E(()=>{"use strict";Ye()});var mt,wt=E(()=>{"use strict";mt="1.22.0-dev.20250409-89f8206ba4"});var ht,C,qe=E(()=>{"use strict";wt();ht="warning",C={wasm:{},webgl:{},webgpu:{},versions:{common:mt},set logLevel(e){if(e!==void 0){if(typeof e!="string"||["verbose","info","warning","error","fatal"].indexOf(e)===-1)throw new Error(`Unsupported logging level: ${e}`);ht=e}},get logLevel(){return ht}};Object.defineProperty(C,"logLevel",{enumerable:!0})});var I,yt=E(()=>{"use strict";qe();I=C});var bt,gt,Et=E(()=>{"use strict";bt=(e,t)=>{let n=typeof document<"u"?document.createElement("canvas"):new OffscreenCanvas(1,1);n.width=e.dims[3],n.height=e.dims[2];let o=n.getContext("2d");if(o!=null){let r,a;t?.tensorLayout!==void 0&&t.tensorLayout==="NHWC"?(r=e.dims[2],a=e.dims[3]):(r=e.dims[3],a=e.dims[2]);let i=t?.format!==void 0?t.format:"RGB",s=t?.norm,u,f;s===void 0||s.mean===void 0?u=[255,255,255,255]:typeof s.mean=="number"?u=[s.mean,s.mean,s.mean,s.mean]:(u=[s.mean[0],s.mean[1],s.mean[2],0],s.mean[3]!==void 0&&(u[3]=s.mean[3])),s===void 0||s.bias===void 0?f=[0,0,0,0]:typeof s.bias=="number"?f=[s.bias,s.bias,s.bias,s.bias]:(f=[s.bias[0],s.bias[1],s.bias[2],0],s.bias[3]!==void 0&&(f[3]=s.bias[3]));let c=a*r,l=0,d=c,p=c*2,w=-1;i==="RGBA"?(l=0,d=c,p=c*2,w=c*3):i==="RGB"?(l=0,d=c,p=c*2):i==="RBG"&&(l=0,p=c,d=c*2);for(let y=0;y<a;y++)for(let O=0;O<r;O++){let m=(e.data[l++]-f[0])*u[0],h=(e.data[d++]-f[1])*u[1],P=(e.data[p++]-f[2])*u[2],g=w===-1?255:(e.data[w++]-f[3])*u[3];o.fillStyle="rgba("+m+","+h+","+P+","+g+")",o.fillRect(O,y,1,1)}if("toDataURL"in n)return n.toDataURL();throw new Error("toDataURL is not supported")}else throw new Error("Can not access image data")},gt=(e,t)=>{let n=typeof document<"u"?document.createElement("canvas").getContext("2d"):new OffscreenCanvas(1,1).getContext("2d"),o;if(n!=null){let r,a,i;t?.tensorLayout!==void 0&&t.tensorLayout==="NHWC"?(r=e.dims[2],a=e.dims[1],i=e.dims[3]):(r=e.dims[3],a=e.dims[2],i=e.dims[1]);let s=t!==void 0&&t.format!==void 0?t.format:"RGB",u=t?.norm,f,c;u===void 0||u.mean===void 0?f=[255,255,255,255]:typeof u.mean=="number"?f=[u.mean,u.mean,u.mean,u.mean]:(f=[u.mean[0],u.mean[1],u.mean[2],255],u.mean[3]!==void 0&&(f[3]=u.mean[3])),u===void 0||u.bias===void 0?c=[0,0,0,0]:typeof u.bias=="number"?c=[u.bias,u.bias,u.bias,u.bias]:(c=[u.bias[0],u.bias[1],u.bias[2],0],u.bias[3]!==void 0&&(c[3]=u.bias[3]));let l=a*r;if(t!==void 0&&(t.format!==void 0&&i===4&&t.format!=="RGBA"||i===3&&t.format!=="RGB"&&t.format!=="BGR"))throw new Error("Tensor format doesn't match input tensor dims");let d=4,p=0,w=1,y=2,O=3,m=0,h=l,P=l*2,g=-1;s==="RGBA"?(m=0,h=l,P=l*2,g=l*3):s==="RGB"?(m=0,h=l,P=l*2):s==="RBG"&&(m=0,P=l,h=l*2),o=n.createImageData(r,a);for(let S=0;S<a*r;p+=d,w+=d,y+=d,O+=d,S++)o.data[p]=(e.data[m++]-c[0])*f[0],o.data[w]=(e.data[h++]-c[1])*f[1],o.data[y]=(e.data[P++]-c[2])*f[2],o.data[O]=g===-1?255:(e.data[g++]-c[3])*f[3]}else throw new Error("Can not access image data");return o}});var Je,Tt,St,At,Ot,It,Pt=E(()=>{"use strict";ge();Je=(e,t)=>{if(e===void 0)throw new Error("Image buffer must be defined");if(t.height===void 0||t.width===void 0)throw new Error("Image height and width must be defined");if(t.tensorLayout==="NHWC")throw new Error("NHWC Tensor layout is not supported yet");let{height:n,width:o}=t,r=t.norm??{mean:255,bias:0},a,i;typeof r.mean=="number"?a=[r.mean,r.mean,r.mean,r.mean]:a=[r.mean[0],r.mean[1],r.mean[2],r.mean[3]??255],typeof r.bias=="number"?i=[r.bias,r.bias,r.bias,r.bias]:i=[r.bias[0],r.bias[1],r.bias[2],r.bias[3]??0];let s=t.format!==void 0?t.format:"RGBA",u=t.tensorFormat!==void 0&&t.tensorFormat!==void 0?t.tensorFormat:"RGB",f=n*o,c=u==="RGBA"?new Float32Array(f*4):new Float32Array(f*3),l=4,d=0,p=1,w=2,y=3,O=0,m=f,h=f*2,P=-1;s==="RGB"&&(l=3,d=0,p=1,w=2,y=-1),u==="RGBA"?P=f*3:u==="RBG"?(O=0,h=f,m=f*2):u==="BGR"&&(h=0,m=f,O=f*2);for(let S=0;S<f;S++,d+=l,w+=l,p+=l,y+=l)c[O++]=(e[d]+i[0])/a[0],c[m++]=(e[p]+i[1])/a[1],c[h++]=(e[w]+i[2])/a[2],P!==-1&&y!==-1&&(c[P++]=(e[y]+i[3])/a[3]);return u==="RGBA"?new B("float32",c,[1,4,n,o]):new B("float32",c,[1,3,n,o])},Tt=async(e,t)=>{let n=typeof HTMLImageElement<"u"&&e instanceof HTMLImageElement,o=typeof ImageData<"u"&&e instanceof ImageData,r=typeof ImageBitmap<"u"&&e instanceof ImageBitmap,a=typeof e=="string",i,s=t??{},u=()=>{if(typeof document<"u")return document.createElement("canvas");if(typeof OffscreenCanvas<"u")return new OffscreenCanvas(1,1);throw new Error("Canvas is not supported")},f=c=>typeof HTMLCanvasElement<"u"&&c instanceof HTMLCanvasElement||c instanceof OffscreenCanvas?c.getContext("2d"):null;if(n){let c=u();c.width=e.width,c.height=e.height;let l=f(c);if(l!=null){let d=e.height,p=e.width;if(t!==void 0&&t.resizedHeight!==void 0&&t.resizedWidth!==void 0&&(d=t.resizedHeight,p=t.resizedWidth),t!==void 0){if(s=t,t.tensorFormat!==void 0)throw new Error("Image input config format must be RGBA for HTMLImageElement");s.tensorFormat="RGBA",s.height=d,s.width=p}else s.tensorFormat="RGBA",s.height=d,s.width=p;l.drawImage(e,0,0),i=l.getImageData(0,0,p,d).data}else throw new Error("Can not access image data")}else if(o){let c,l;if(t!==void 0&&t.resizedWidth!==void 0&&t.resizedHeight!==void 0?(c=t.resizedHeight,l=t.resizedWidth):(c=e.height,l=e.width),t!==void 0&&(s=t),s.format="RGBA",s.height=c,s.width=l,t!==void 0){let d=u();d.width=l,d.height=c;let p=f(d);if(p!=null)p.putImageData(e,0,0),i=p.getImageData(0,0,l,c).data;else throw new Error("Can not access image data")}else i=e.data}else if(r){if(t===void 0)throw new Error("Please provide image config with format for Imagebitmap");let c=u();c.width=e.width,c.height=e.height;let l=f(c);if(l!=null){let d=e.height,p=e.width;return l.drawImage(e,0,0,p,d),i=l.getImageData(0,0,p,d).data,s.height=d,s.width=p,Je(i,s)}else throw new Error("Can not access image data")}else{if(a)return new Promise((c,l)=>{let d=u(),p=f(d);if(!e||!p)return l();let w=new Image;w.crossOrigin="Anonymous",w.src=e,w.onload=()=>{d.width=w.width,d.height=w.height,p.drawImage(w,0,0,d.width,d.height);let y=p.getImageData(0,0,d.width,d.height);s.height=d.height,s.width=d.width,c(Je(y.data,s))}});throw new Error("Input data provided is not supported - aborted tensor creation")}if(i!==void 0)return Je(i,s);throw new Error("Input data provided is not supported - aborted tensor creation")},St=(e,t)=>{let{width:n,height:o,download:r,dispose:a}=t,i=[1,o,n,4];return new B({location:"texture",type:"float32",texture:e,dims:i,download:r,dispose:a})},At=(e,t)=>{let{dataType:n,dims:o,download:r,dispose:a}=t;return new B({location:"gpu-buffer",type:n??"float32",gpuBuffer:e,dims:o,download:r,dispose:a})},Ot=(e,t)=>{let{dataType:n,dims:o,download:r,dispose:a}=t;return new B({location:"ml-tensor",type:n??"float32",mlTensor:e,dims:o,download:r,dispose:a})},It=(e,t,n)=>new B({location:"cpu-pinned",type:e,data:t,dims:n??[t.length]})});var j,fe,vt,xt,Lt=E(()=>{"use strict";j=new Map([["float32",Float32Array],["uint8",Uint8Array],["int8",Int8Array],["uint16",Uint16Array],["int16",Int16Array],["int32",Int32Array],["bool",Uint8Array],["float64",Float64Array],["uint32",Uint32Array],["int4",Uint8Array],["uint4",Uint8Array]]),fe=new Map([[Float32Array,"float32"],[Uint8Array,"uint8"],[Int8Array,"int8"],[Uint16Array,"uint16"],[Int16Array,"int16"],[Int32Array,"int32"],[Float64Array,"float64"],[Uint32Array,"uint32"]]),vt=!1,xt=()=>{if(!vt){vt=!0;let e=typeof BigInt64Array<"u"&&BigInt64Array.from,t=typeof BigUint64Array<"u"&&BigUint64Array.from,n=globalThis.Float16Array,o=typeof n<"u"&&n.from;e&&(j.set("int64",BigInt64Array),fe.set(BigInt64Array,"int64")),t&&(j.set("uint64",BigUint64Array),fe.set(BigUint64Array,"uint64")),o?(j.set("float16",n),fe.set(n,"float16")):j.set("float16",Uint16Array)}}});var Bt,Ut,_t=E(()=>{"use strict";ge();Bt=e=>{let t=1;for(let n=0;n<e.length;n++){let o=e[n];if(typeof o!="number"||!Number.isSafeInteger(o))throw new TypeError(`dims[${n}] must be an integer, got: ${o}`);if(o<0)throw new RangeError(`dims[${n}] must be a non-negative integer, got: ${o}`);t*=o}return t},Ut=(e,t)=>{switch(e.location){case"cpu":return new B(e.type,e.data,t);case"cpu-pinned":return new B({location:"cpu-pinned",data:e.data,type:e.type,dims:t});case"texture":return new B({location:"texture",texture:e.texture,type:e.type,dims:t});case"gpu-buffer":return new B({location:"gpu-buffer",gpuBuffer:e.gpuBuffer,type:e.type,dims:t});case"ml-tensor":return new B({location:"ml-tensor",mlTensor:e.mlTensor,type:e.type,dims:t});default:throw new Error(`tensorReshape: tensor location ${e.location} is not supported`)}}});var B,ge=E(()=>{"use strict";Et();Pt();Lt();_t();B=class{constructor(t,n,o){xt();let r,a;if(typeof t=="object"&&"location"in t)switch(this.dataLocation=t.location,r=t.type,a=t.dims,t.location){case"cpu-pinned":{let s=j.get(r);if(!s)throw new TypeError(`unsupported type "${r}" to create tensor from pinned buffer`);if(!(t.data instanceof s))throw new TypeError(`buffer should be of type ${s.name}`);this.cpuData=t.data;break}case"texture":{if(r!=="float32")throw new TypeError(`unsupported type "${r}" to create tensor from texture`);this.gpuTextureData=t.texture,this.downloader=t.download,this.disposer=t.dispose;break}case"gpu-buffer":{if(r!=="float32"&&r!=="float16"&&r!=="int32"&&r!=="int64"&&r!=="uint32"&&r!=="uint8"&&r!=="bool"&&r!=="uint4"&&r!=="int4")throw new TypeError(`unsupported type "${r}" to create tensor from gpu buffer`);this.gpuBufferData=t.gpuBuffer,this.downloader=t.download,this.disposer=t.dispose;break}case"ml-tensor":{if(r!=="float32"&&r!=="float16"&&r!=="int32"&&r!=="int64"&&r!=="uint32"&&r!=="uint64"&&r!=="int8"&&r!=="uint8"&&r!=="bool"&&r!=="uint4"&&r!=="int4")throw new TypeError(`unsupported type "${r}" to create tensor from MLTensor`);this.mlTensorData=t.mlTensor,this.downloader=t.download,this.disposer=t.dispose;break}default:throw new Error(`Tensor constructor: unsupported location '${this.dataLocation}'`)}else{let s,u;if(typeof t=="string")if(r=t,u=o,t==="string"){if(!Array.isArray(n))throw new TypeError("A string tensor's data must be a string array.");s=n}else{let f=j.get(t);if(f===void 0)throw new TypeError(`Unsupported tensor type: ${t}.`);if(Array.isArray(n)){if(t==="float16"&&f===Uint16Array||t==="uint4"||t==="int4")throw new TypeError(`Creating a ${t} tensor from number array is not supported. Please use ${f.name} as data.`);t==="uint64"||t==="int64"?s=f.from(n,BigInt):s=f.from(n)}else if(n instanceof f)s=n;else if(n instanceof Uint8ClampedArray)if(t==="uint8")s=Uint8Array.from(n);else throw new TypeError("A Uint8ClampedArray tensor's data must be type of uint8");else if(t==="float16"&&n instanceof Uint16Array&&f!==Uint16Array)s=new globalThis.Float16Array(n.buffer,n.byteOffset,n.length);else throw new TypeError(`A ${r} tensor's data must be type of ${f}`)}else if(u=n,Array.isArray(t)){if(t.length===0)throw new TypeError("Tensor type cannot be inferred from an empty array.");let f=typeof t[0];if(f==="string")r="string",s=t;else if(f==="boolean")r="bool",s=Uint8Array.from(t);else throw new TypeError(`Invalid element type of data array: ${f}.`)}else if(t instanceof Uint8ClampedArray)r="uint8",s=Uint8Array.from(t);else{let f=fe.get(t.constructor);if(f===void 0)throw new TypeError(`Unsupported type for tensor data: ${t.constructor}.`);r=f,s=t}if(u===void 0)u=[s.length];else if(!Array.isArray(u))throw new TypeError("A tensor's dims must be a number array");a=u,this.cpuData=s,this.dataLocation="cpu"}let i=Bt(a);if(this.cpuData&&i!==this.cpuData.length&&!((r==="uint4"||r==="int4")&&Math.ceil(i/2)===this.cpuData.length))throw new Error(`Tensor's size(${i}) does not match data length(${this.cpuData.length}).`);this.type=r,this.dims=a,this.size=i}static async fromImage(t,n){return Tt(t,n)}static fromTexture(t,n){return St(t,n)}static fromGpuBuffer(t,n){return At(t,n)}static fromMLTensor(t,n){return Ot(t,n)}static fromPinnedBuffer(t,n,o){return It(t,n,o)}toDataURL(t){return bt(this,t)}toImageData(t){return gt(this,t)}get data(){if(this.ensureValid(),!this.cpuData)throw new Error("The data is not on CPU. Use `getData()` to download GPU data to CPU, or use `texture` or `gpuBuffer` property to access the GPU data directly.");return this.cpuData}get location(){return this.dataLocation}get texture(){if(this.ensureValid(),!this.gpuTextureData)throw new Error("The data is not stored as a WebGL texture.");return this.gpuTextureData}get gpuBuffer(){if(this.ensureValid(),!this.gpuBufferData)throw new Error("The data is not stored as a WebGPU buffer.");return this.gpuBufferData}get mlTensor(){if(this.ensureValid(),!this.mlTensorData)throw new Error("The data is not stored as a WebNN MLTensor.");return this.mlTensorData}async getData(t){switch(this.ensureValid(),this.dataLocation){case"cpu":case"cpu-pinned":return this.data;case"texture":case"gpu-buffer":case"ml-tensor":{if(!this.downloader)throw new Error("The current tensor is not created with a specified data downloader.");if(this.isDownloading)throw new Error("The current tensor is being downloaded.");try{this.isDownloading=!0;let n=await this.downloader();return this.downloader=void 0,this.dataLocation="cpu",this.cpuData=n,t&&this.disposer&&(this.disposer(),this.disposer=void 0),n}finally{this.isDownloading=!1}}default:throw new Error(`cannot get data from location: ${this.dataLocation}`)}}dispose(){if(this.isDownloading)throw new Error("The current tensor is being downloaded.");this.disposer&&(this.disposer(),this.disposer=void 0),this.cpuData=void 0,this.gpuTextureData=void 0,this.gpuBufferData=void 0,this.mlTensorData=void 0,this.downloader=void 0,this.isDownloading=void 0,this.dataLocation="none"}ensureValid(){if(this.dataLocation==="none")throw new Error("The tensor is disposed.")}reshape(t){if(this.ensureValid(),this.downloader||this.disposer)throw new Error("Cannot reshape a tensor that owns GPU resource.");return Ut(this,t)}}});var W,Ze=E(()=>{"use strict";ge();W=B});var Mt,Dt,V,Y,Xe=E(()=>{"use strict";qe();Mt=(e,t)=>{(typeof C.trace>"u"?!C.wasm.trace:!C.trace)||console.timeStamp(`${e}::ORT::${t}`)},Dt=(e,t)=>{let n=new Error().stack?.split(/\r\n|\r|\n/g)||[],o=!1;for(let r=0;r<n.length;r++){if(o&&!n[r].includes("TRACE_FUNC")){let a=`FUNC_${e}::${n[r].trim().split(" ")[1]}`;t&&(a+=`::${t}`),Mt("CPU",a);return}n[r].includes("TRACE_FUNC")&&(o=!0)}},V=e=>{(typeof C.trace>"u"?!C.wasm.trace:!C.trace)||Dt("BEGIN",e)},Y=e=>{(typeof C.trace>"u"?!C.wasm.trace:!C.trace)||Dt("END",e)}});var Ee,Ct=E(()=>{"use strict";Ye();Ze();Xe();Ee=class e{constructor(t){this.handler=t}async run(t,n,o){V();let r={},a={};if(typeof t!="object"||t===null||t instanceof W||Array.isArray(t))throw new TypeError("'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.");let i=!0;if(typeof n=="object"){if(n===null)throw new TypeError("Unexpected argument[1]: cannot be null.");if(n instanceof W)throw new TypeError("'fetches' cannot be a Tensor");if(Array.isArray(n)){if(n.length===0)throw new TypeError("'fetches' cannot be an empty array.");i=!1;for(let f of n){if(typeof f!="string")throw new TypeError("'fetches' must be a string array or an object.");if(this.outputNames.indexOf(f)===-1)throw new RangeError(`'fetches' contains invalid output name: ${f}.`);r[f]=null}if(typeof o=="object"&&o!==null)a=o;else if(typeof o<"u")throw new TypeError("'options' must be an object.")}else{let f=!1,c=Object.getOwnPropertyNames(n);for(let l of this.outputNames)if(c.indexOf(l)!==-1){let d=n[l];(d===null||d instanceof W)&&(f=!0,i=!1,r[l]=d)}if(f){if(typeof o=="object"&&o!==null)a=o;else if(typeof o<"u")throw new TypeError("'options' must be an object.")}else a=n}}else if(typeof n<"u")throw new TypeError("Unexpected argument[1]: must be 'fetches' or 'options'.");for(let f of this.inputNames)if(typeof t[f]>"u")throw new Error(`input '${f}' is missing in 'feeds'.`);if(i)for(let f of this.outputNames)r[f]=null;let s=await this.handler.run(t,r,a),u={};for(let f in s)if(Object.hasOwnProperty.call(s,f)){let c=s[f];c instanceof W?u[f]=c:u[f]=new W(c.type,c.data,c.dims)}return Y(),u}async release(){return this.handler.dispose()}static async create(t,n,o,r){V();let a,i={};if(typeof t=="string"){if(a=t,typeof n=="object"&&n!==null)i=n;else if(typeof n<"u")throw new TypeError("'options' must be an object.")}else if(t instanceof Uint8Array){if(a=t,typeof n=="object"&&n!==null)i=n;else if(typeof n<"u")throw new TypeError("'options' must be an object.")}else if(t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer){let c=t,l=0,d=t.byteLength;if(typeof n=="object"&&n!==null)i=n;else if(typeof n=="number"){if(l=n,!Number.isSafeInteger(l))throw new RangeError("'byteOffset' must be an integer.");if(l<0||l>=c.byteLength)throw new RangeError(`'byteOffset' is out of range [0, ${c.byteLength}).`);if(d=t.byteLength-l,typeof o=="number"){if(d=o,!Number.isSafeInteger(d))throw new RangeError("'byteLength' must be an integer.");if(d<=0||l+d>c.byteLength)throw new RangeError(`'byteLength' is out of range (0, ${c.byteLength-l}].`);if(typeof r=="object"&&r!==null)i=r;else if(typeof r<"u")throw new TypeError("'options' must be an object.")}else if(typeof o<"u")throw new TypeError("'byteLength' must be a number.")}else if(typeof n<"u")throw new TypeError("'options' must be an object.");a=new Uint8Array(c,l,d)}else throw new TypeError("Unexpected argument[0]: must be 'path' or 'buffer'.");let[s,u]=await lt(i),f=await s.createInferenceSessionHandler(a,u);return Y(),new e(f)}startProfiling(){this.handler.startProfiling()}endProfiling(){this.handler.endProfiling()}get inputNames(){return this.handler.inputNames}get outputNames(){return this.handler.outputNames}get inputMetadata(){return this.handler.inputMetadata}get outputMetadata(){return this.handler.outputMetadata}}});var vn,Rt=E(()=>{"use strict";Ct();vn=Ee});var Ft=E(()=>{"use strict"});var kt=E(()=>{"use strict"});var Nt=E(()=>{"use strict"});var Wt=E(()=>{"use strict"});var Ke={};Ve(Ke,{InferenceSession:()=>vn,TRACE:()=>Mt,TRACE_FUNC_BEGIN:()=>V,TRACE_FUNC_END:()=>Y,Tensor:()=>W,env:()=>I,registerBackend:()=>ue});var q=E(()=>{"use strict";pt();yt();Rt();Ze();Ft();kt();Xe();Nt();Wt()});var Te=E(()=>{"use strict"});var Ht={};Ve(Ht,{default:()=>xn});var Gt,zt,xn,jt=E(()=>{"use strict";Qe();J();Se();Gt="ort-wasm-proxy-worker",zt=globalThis.self?.name===Gt;zt&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Ae(n.wasm).then(()=>{Oe(n).then(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})})},o=>{postMessage({type:t,err:o})});break;case"init-ep":{let{epName:o,env:r}=n;Ie(r,o).then(()=>{postMessage({type:t})},a=>{postMessage({type:t,err:a})});break}case"copy-from":{let{buffer:o}=n,r=ce(o);postMessage({type:t,out:r});break}case"create":{let{model:o,options:r}=n;Pe(o,r).then(a=>{postMessage({type:t,out:a})},a=>{postMessage({type:t,err:a})});break}case"release":ve(n),postMessage({type:t});break;case"run":{let{sessionId:o,inputIndices:r,inputs:a,outputIndices:i,options:s}=n;xe(o,r,a,i,new Array(i.length).fill(null),s).then(u=>{u.some(f=>f[3]!=="cpu")?postMessage({type:t,err:"Proxy does not support non-cpu tensor location."}):postMessage({type:t,out:u},Be([...a,...u]))},u=>{postMessage({type:t,err:u})});break}case"end-profiling":Le(n),postMessage({type:t});break;default:}}catch(o){postMessage({type:t,err:o})}});xn=zt?null:e=>new Worker(e??R,{type:"module",name:Gt})});var qt,Ln,Bn,R,Ue,et,Un,_n,Jt,Mn,Vt,Zt,Yt,Xt,Se=E(()=>{"use strict";Te();qt=typeof location>"u"?void 0:location.origin,Ln=import.meta.url>"file:"&&import.meta.url<"file;",Bn=()=>{if(!!1){if(Ln){let e=URL;return new URL(new e("ort.wasm.min.mjs",import.meta.url).href,qt).href}return import.meta.url}},R=Bn(),Ue=()=>{if(R&&!R.startsWith("blob:"))return R.substring(0,R.lastIndexOf("/")+1)},et=(e,t)=>{try{let n=t??R;return(n?new URL(e,n):new URL(e)).origin===qt}catch{return!1}},Un=(e,t)=>{let n=t??R;try{return(n?new URL(e,n):new URL(e)).href}catch{return}},_n=(e,t)=>`${t??"./"}${e}`,Jt=async e=>{let n=await(await fetch(e,{credentials:"same-origin"})).blob();return URL.createObjectURL(n)},Mn=async e=>(await import(/*webpackIgnore:true*/e)).default,Vt=(jt(),dt(Ht)).default,Zt=async()=>{if(!R)throw new Error("Failed to load proxy worker: cannot determine the script source URL.");if(et(R))return[void 0,Vt()];let e=await Jt(R);return[e,Vt(e)]},Yt=void 0,Xt=async(e,t,n)=>{if(!e&&!t&&Yt&&R&&et(R))return[void 0,Yt];{let o="ort-wasm-simd-threaded.mjs",r=e??Un(o,t),a=!!1&&n&&r&&!et(r,t),i=a?await Jt(r):r??_n(o,t);return[a?i:void 0,await Mn(i)]}}});var tt,nt,_e,Kt,Dn,Cn,Rn,Ae,A,J=E(()=>{"use strict";Se();nt=!1,_e=!1,Kt=!1,Dn=()=>{if(typeof SharedArrayBuffer>"u")return!1;try{return typeof MessageChannel<"u"&&new MessageChannel().port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11]))}catch{return!1}},Cn=()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,30,1,28,0,65,0,253,15,253,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,253,186,1,26,11]))}catch{return!1}},Rn=()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,19,1,17,0,65,1,253,15,65,2,253,15,65,3,253,15,253,147,2,11]))}catch{return!1}},Ae=async e=>{if(nt)return Promise.resolve();if(_e)throw new Error("multiple calls to 'initializeWebAssembly()' detected.");if(Kt)throw new Error("previous call to 'initializeWebAssembly()' failed.");_e=!0;let t=e.initTimeout,n=e.numThreads;if(e.simd!==!1){if(e.simd==="relaxed"){if(!Rn())throw new Error("Relaxed WebAssembly SIMD is not supported in the current environment.")}else if(!Cn())throw new Error("WebAssembly SIMD is not supported in the current environment.")}let o=Dn();n>1&&!o&&(typeof self<"u"&&!self.crossOriginIsolated&&console.warn("env.wasm.numThreads is set to "+n+", but this will not work unless you enable crossOriginIsolated mode. See https://web.dev/cross-origin-isolation-guide/ for more info."),console.warn("WebAssembly multi-threading is not supported in the current environment. Falling back to single-threading."),e.numThreads=n=1);let r=e.wasmPaths,a=typeof r=="string"?r:void 0,i=r?.mjs,s=i?.href??i,u=r?.wasm,f=u?.href??u,c=e.wasmBinary,[l,d]=await Xt(s,a,n>1),p=!1,w=[];if(t>0&&w.push(new Promise(y=>{setTimeout(()=>{p=!0,y()},t)})),w.push(new Promise((y,O)=>{let m={numThreads:n};if(c)m.wasmBinary=c;else if(f||a)m.locateFile=h=>f??a+h;else if(s&&s.indexOf("blob:")!==0)m.locateFile=h=>new URL(h,s).href;else if(l){let h=Ue();h&&(m.locateFile=P=>h+P)}d(m).then(h=>{_e=!1,nt=!0,tt=h,y(),l&&URL.revokeObjectURL(l)},h=>{_e=!1,Kt=!0,O(h)})})),await Promise.race(w),p)throw new Error(`WebAssembly backend initializing failed due to timeout: ${t}ms`)},A=()=>{if(nt&&tt)return tt;throw new Error("WebAssembly is not initialized yet.")}});var F,de,T,Me=E(()=>{"use strict";J();F=(e,t)=>{let n=A(),o=n.lengthBytesUTF8(e)+1,r=n._malloc(o);return n.stringToUTF8(e,r,o),t.push(r),r},de=(e,t,n,o)=>{if(typeof e=="object"&&e!==null){if(n.has(e))throw new Error("Circular reference in options");n.add(e)}Object.entries(e).forEach(([r,a])=>{let i=t?t+r:r;if(typeof a=="object")de(a,i+".",n,o);else if(typeof a=="string"||typeof a=="number")o(i,a.toString());else if(typeof a=="boolean")o(i,a?"1":"0");else throw new Error(`Can't handle extra config type: ${typeof a}`)})},T=e=>{let t=A(),n=t.stackSave();try{let o=t.PTR_SIZE,r=t.stackAlloc(2*o);t._OrtGetLastError(r,r+o);let a=Number(t.getValue(r,o===4?"i32":"i64")),i=t.getValue(r+o,"*"),s=i?t.UTF8ToString(i):"";throw new Error(`${e} ERROR_CODE: ${a}, ERROR_MESSAGE: ${s}`)}finally{t.stackRestore(n)}}});var Qt,en=E(()=>{"use strict";J();Me();Qt=e=>{let t=A(),n=0,o=[],r=e||{};try{if(e?.logSeverityLevel===void 0)r.logSeverityLevel=2;else if(typeof e.logSeverityLevel!="number"||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);if(e?.logVerbosityLevel===void 0)r.logVerbosityLevel=0;else if(typeof e.logVerbosityLevel!="number"||!Number.isInteger(e.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);e?.terminate===void 0&&(r.terminate=!1);let a=0;return e?.tag!==void 0&&(a=F(e.tag,o)),n=t._OrtCreateRunOptions(r.logSeverityLevel,r.logVerbosityLevel,!!r.terminate,a),n===0&&T("Can't create run options."),e?.extra!==void 0&&de(e.extra,"",new WeakSet,(i,s)=>{let u=F(i,o),f=F(s,o);t._OrtAddRunConfigEntry(n,u,f)!==0&&T(`Can't set a run config entry: ${i} - ${s}.`)}),[n,o]}catch(a){throw n!==0&&t._OrtReleaseRunOptions(n),o.forEach(i=>t._free(i)),a}}});var Fn,kn,Nn,De,Wn,tn,nn=E(()=>{"use strict";J();Me();Fn=e=>{switch(e){case"disabled":return 0;case"basic":return 1;case"extended":return 2;case"all":return 99;default:throw new Error(`unsupported graph optimization level: ${e}`)}},kn=e=>{switch(e){case"sequential":return 0;case"parallel":return 1;default:throw new Error(`unsupported execution mode: ${e}`)}},Nn=e=>{e.extra||(e.extra={}),e.extra.session||(e.extra.session={});let t=e.extra.session;t.use_ort_model_bytes_directly||(t.use_ort_model_bytes_directly="1"),e.executionProviders&&e.executionProviders.some(n=>(typeof n=="string"?n:n.name)==="webgpu")&&(e.enableMemPattern=!1)},De=(e,t,n,o)=>{let r=F(t,o),a=F(n,o);A()._OrtAddSessionConfigEntry(e,r,a)!==0&&T(`Can't set a session config entry: ${t} - ${n}.`)},Wn=async(e,t,n)=>{for(let o of t){let r=typeof o=="string"?o:o.name,a=[];switch(r){case"webnn":if(r="WEBNN",typeof o!="string"){let l=o?.deviceType;l&&De(e,"deviceType",l,n)}break;case"webgpu":if(r="JS",typeof o!="string"){let c=o;if(c?.preferredLayout){if(c.preferredLayout!=="NCHW"&&c.preferredLayout!=="NHWC")throw new Error(`preferredLayout must be either 'NCHW' or 'NHWC': ${c.preferredLayout}`);De(e,"preferredLayout",c.preferredLayout,n)}}break;case"wasm":case"cpu":continue;default:throw new Error(`not supported execution provider: ${r}`)}let i=F(r,n),s=a.length,u=0,f=0;if(s>0){u=A()._malloc(s*A().PTR_SIZE),n.push(u),f=A()._malloc(s*A().PTR_SIZE),n.push(f);for(let c=0;c<s;c++)A().setValue(u+c*A().PTR_SIZE,a[c][0],"*"),A().setValue(f+c*A().PTR_SIZE,a[c][1],"*")}await A()._OrtAppendExecutionProvider(e,i,u,f,s)!==0&&T(`Can't append execution provider: ${r}.`)}},tn=async e=>{let t=A(),n=0,o=[],r=e||{};Nn(r);try{let a=Fn(r.graphOptimizationLevel??"all"),i=kn(r.executionMode??"sequential"),s=typeof r.logId=="string"?F(r.logId,o):0,u=r.logSeverityLevel??2;if(!Number.isInteger(u)||u<0||u>4)throw new Error(`log serverity level is not valid: ${u}`);let f=r.logVerbosityLevel??0;if(!Number.isInteger(f)||f<0||f>4)throw new Error(`log verbosity level is not valid: ${f}`);let c=typeof r.optimizedModelFilePath=="string"?F(r.optimizedModelFilePath,o):0;if(n=t._OrtCreateSessionOptions(a,!!r.enableCpuMemArena,!!r.enableMemPattern,i,!!r.enableProfiling,0,s,u,f,c),n===0&&T("Can't create session options."),r.executionProviders&&await Wn(n,r.executionProviders,o),r.enableGraphCapture!==void 0){if(typeof r.enableGraphCapture!="boolean")throw new Error(`enableGraphCapture must be a boolean value: ${r.enableGraphCapture}`);De(n,"enableGraphCapture",r.enableGraphCapture.toString(),o)}if(r.freeDimensionOverrides)for(let[l,d]of Object.entries(r.freeDimensionOverrides)){if(typeof l!="string")throw new Error(`free dimension override name must be a string: ${l}`);if(typeof d!="number"||!Number.isInteger(d)||d<0)throw new Error(`free dimension override value must be a non-negative integer: ${d}`);let p=F(l,o);t._OrtAddFreeDimensionOverride(n,p,d)!==0&&T(`Can't set a free dimension override: ${l} - ${d}.`)}return r.extra!==void 0&&de(r.extra,"",new WeakSet,(l,d)=>{De(n,l,d,o)}),[n,o]}catch(a){throw n!==0&&t._OrtReleaseSessionOptions(n)!==0&&T("Can't release session options."),o.forEach(i=>t._free(i)),a}}});var ne,Ce,re,rn,on,Re,Fe,sn,rt=E(()=>{"use strict";ne=e=>{switch(e){case"int8":return 3;case"uint8":return 2;case"bool":return 9;case"int16":return 5;case"uint16":return 4;case"int32":return 6;case"uint32":return 12;case"float16":return 10;case"float32":return 1;case"float64":return 11;case"string":return 8;case"int64":return 7;case"uint64":return 13;case"int4":return 22;case"uint4":return 21;default:throw new Error(`unsupported data type: ${e}`)}},Ce=e=>{switch(e){case 3:return"int8";case 2:return"uint8";case 9:return"bool";case 5:return"int16";case 4:return"uint16";case 6:return"int32";case 12:return"uint32";case 10:return"float16";case 1:return"float32";case 11:return"float64";case 8:return"string";case 7:return"int64";case 13:return"uint64";case 22:return"int4";case 21:return"uint4";default:throw new Error(`unsupported data type: ${e}`)}},re=(e,t)=>{let n=[-1,4,1,1,2,2,4,8,-1,1,2,8,4,8,-1,-1,-1,-1,-1,-1,-1,.5,.5][e],o=typeof t=="number"?t:t.reduce((r,a)=>r*a,1);return n>0?Math.ceil(o*n):void 0},rn=e=>{switch(e){case"float16":return typeof Float16Array<"u"&&Float16Array.from?Float16Array:Uint16Array;case"float32":return Float32Array;case"uint8":return Uint8Array;case"int8":return Int8Array;case"uint16":return Uint16Array;case"int16":return Int16Array;case"int32":return Int32Array;case"bool":return Uint8Array;case"float64":return Float64Array;case"uint32":return Uint32Array;case"int64":return BigInt64Array;case"uint64":return BigUint64Array;default:throw new Error(`unsupported type: ${e}`)}},on=e=>{switch(e){case"verbose":return 0;case"info":return 1;case"warning":return 2;case"error":return 3;case"fatal":return 4;default:throw new Error(`unsupported logging level: ${e}`)}},Re=e=>e==="float32"||e==="float16"||e==="int32"||e==="int64"||e==="uint32"||e==="uint8"||e==="bool"||e==="uint4"||e==="int4",Fe=e=>e==="float32"||e==="float16"||e==="int32"||e==="int64"||e==="uint32"||e==="uint64"||e==="int8"||e==="uint8"||e==="bool"||e==="uint4"||e==="int4",sn=e=>{switch(e){case"none":return 0;case"cpu":return 1;case"cpu-pinned":return 2;case"texture":return 3;case"gpu-buffer":return 4;case"ml-tensor":return 5;default:throw new Error(`unsupported data location: ${e}`)}}});var le,ot=E(()=>{"use strict";Te();le=async e=>{if(typeof e=="string")if(!1)try{let{readFile:t}=je("node:fs/promises");return new Uint8Array(await t(e))}catch(t){if(t.code==="ERR_FS_FILE_TOO_LARGE"){let{createReadStream:n}=je("node:fs"),o=n(e),r=[];for await(let a of o)r.push(a);return new Uint8Array(Buffer.concat(r))}throw t}else{let t=await fetch(e);if(!t.ok)throw new Error(`failed to load external data file: ${e}`);let n=t.headers.get("Content-Length"),o=n?parseInt(n,10):0;if(o<1073741824)return new Uint8Array(await t.arrayBuffer());{if(!t.body)throw new Error(`failed to load external data file: ${e}, no response body.`);let r=t.body.getReader(),a;try{a=new ArrayBuffer(o)}catch(s){if(s instanceof RangeError){let u=Math.ceil(o/65536);a=new WebAssembly.Memory({initial:u,maximum:u}).buffer}else throw s}let i=0;for(;;){let{done:s,value:u}=await r.read();if(s)break;let f=u.byteLength;new Uint8Array(a,i,f).set(u),i+=f}return new Uint8Array(a,0,o)}}else return e instanceof Blob?new Uint8Array(await e.arrayBuffer()):e instanceof Uint8Array?e:new Uint8Array(e)}});var $n,Oe,Ie,oe,Gn,an,ce,Pe,ve,un,xe,Le,Be,Qe=E(()=>{"use strict";en();nn();rt();J();Me();ot();$n=(e,t)=>{A()._OrtInit(e,t)!==0&&T("Can't initialize onnxruntime.")},Oe=async e=>{$n(e.wasm.numThreads,on(e.logLevel))},Ie=async(e,t)=>{A().asyncInit?.()},oe=new Map,Gn=e=>{let t=A(),n=t.stackSave();try{let o=t.PTR_SIZE,r=t.stackAlloc(2*o);t._OrtGetInputOutputCount(e,r,r+o)!==0&&T("Can't get session input/output count.");let i=o===4?"i32":"i64";return[Number(t.getValue(r,i)),Number(t.getValue(r+o,i))]}finally{t.stackRestore(n)}},an=(e,t)=>{let n=A(),o=n.stackSave(),r=0;try{let a=n.PTR_SIZE,i=n.stackAlloc(2*a);n._OrtGetInputOutputMetadata(e,t,i,i+a)!==0&&T("Can't get session input/output metadata.");let u=Number(n.getValue(i,"*"));r=Number(n.getValue(i+a,"*"));let f=n.HEAP32[r/4];if(f===0)return[u,0];let c=n.HEAPU32[r/4+1],l=[];for(let d=0;d<c;d++){let p=Number(n.getValue(r+8+d*a,"*"));l.push(p!==0?n.UTF8ToString(p):Number(n.getValue(r+8+(d+c)*a,"*")))}return[u,f,l]}finally{n.stackRestore(o),r!==0&&n._OrtFree(r)}},ce=e=>{let t=A(),n=t._malloc(e.byteLength);if(n===0)throw new Error(`Can't create a session. failed to allocate a buffer of size ${e.byteLength}.`);return t.HEAPU8.set(e,n),[n,e.byteLength]},Pe=async(e,t)=>{let n,o,r=A();Array.isArray(e)?[n,o]=e:e.buffer===r.HEAPU8.buffer?[n,o]=[e.byteOffset,e.byteLength]:[n,o]=ce(e);let a=0,i=0,s=0,u=[],f=[],c=[];try{if([i,u]=await tn(t),t?.externalData&&r.mountExternalData){let g=[];for(let S of t.externalData){let x=typeof S=="string"?S:S.path;g.push(le(typeof S=="string"?S:S.data).then(_=>{r.mountExternalData(x,_)}))}await Promise.all(g)}for(let g of t?.executionProviders??[])if((typeof g=="string"?g:g.name)==="webnn"){if(r.shouldTransferToMLTensor=!1,typeof g!="string"){let x=g,_=x?.context,M=x?.gpuDevice,K=x?.deviceType,me=x?.powerPreference;_?r.currentContext=_:M?r.currentContext=await r.webnnCreateMLContext(M):r.currentContext=await r.webnnCreateMLContext({deviceType:K,powerPreference:me})}else r.currentContext=await r.webnnCreateMLContext();break}a=await r._OrtCreateSession(n,o,i),r.webgpuOnCreateSession?.(a),a===0&&T("Can't create a session."),r.jsepOnCreateSession?.(),r.currentContext&&(r.webnnRegisterMLContext(a,r.currentContext),r.currentContext=void 0,r.shouldTransferToMLTensor=!0);let[l,d]=Gn(a),p=!!t?.enableGraphCapture,w=[],y=[],O=[],m=[],h=[];for(let g=0;g<l;g++){let[S,x,_]=an(a,g);S===0&&T("Can't get an input name."),f.push(S);let M=r.UTF8ToString(S);w.push(M),O.push(x===0?{name:M,isTensor:!1}:{name:M,isTensor:!0,type:Ce(x),shape:_})}for(let g=0;g<d;g++){let[S,x,_]=an(a,g+l);S===0&&T("Can't get an output name."),c.push(S);let M=r.UTF8ToString(S);y.push(M),m.push(x===0?{name:M,isTensor:!1}:{name:M,isTensor:!0,type:Ce(x),shape:_})}return oe.set(a,[a,f,c,null,p,!1]),[a,w,y,O,m]}catch(l){throw f.forEach(d=>r._OrtFree(d)),c.forEach(d=>r._OrtFree(d)),s!==0&&r._OrtReleaseBinding(s)!==0&&T("Can't release IO binding."),a!==0&&r._OrtReleaseSession(a)!==0&&T("Can't release session."),l}finally{r._free(n),i!==0&&r._OrtReleaseSessionOptions(i)!==0&&T("Can't release session options."),u.forEach(l=>r._free(l)),r.unmountExternalData?.()}},ve=e=>{let t=A(),n=oe.get(e);if(!n)throw new Error(`cannot release session. invalid session id: ${e}`);let[o,r,a,i,s]=n;i&&(s&&t._OrtClearBoundOutputs(i.handle)!==0&&T("Can't clear bound outputs."),t._OrtReleaseBinding(i.handle)!==0&&T("Can't release IO binding.")),t.jsepOnReleaseSession?.(e),t.webnnOnReleaseSession?.(e),t.webgpuOnReleaseSession?.(e),r.forEach(u=>t._OrtFree(u)),a.forEach(u=>t._OrtFree(u)),t._OrtReleaseSession(o)!==0&&T("Can't release session."),oe.delete(e)},un=async(e,t,n,o,r,a,i=!1)=>{if(!e){t.push(0);return}let s=A(),u=s.PTR_SIZE,f=e[0],c=e[1],l=e[3],d=l,p,w;if(f==="string"&&(l==="gpu-buffer"||l==="ml-tensor"))throw new Error("String tensor is not supported on GPU.");if(i&&l!=="gpu-buffer")throw new Error(`External buffer must be provided for input/output index ${a} when enableGraphCapture is true.`);if(l==="gpu-buffer"){let m=e[2].gpuBuffer;w=re(ne(f),c);{let h=s.jsepRegisterBuffer;if(!h)throw new Error('Tensor location "gpu-buffer" is not supported without using WebGPU.');p=h(o,a,m,w)}}else if(l==="ml-tensor"){let m=e[2].mlTensor;w=re(ne(f),c);let h=s.webnnRegisterMLTensor;if(!h)throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');p=h(o,m,ne(f),c)}else{let m=e[2];if(Array.isArray(m)){w=u*m.length,p=s._malloc(w),n.push(p);for(let h=0;h<m.length;h++){if(typeof m[h]!="string")throw new TypeError(`tensor data at index ${h} is not a string`);s.setValue(p+h*u,F(m[h],n),"*")}}else{let h=s.webnnIsGraphInput;if(f!=="string"&&h){let P=s.UTF8ToString(r);if(h(o,P)){let g=ne(f);w=re(g,c),d="ml-tensor";let S=s.webnnCreateTemporaryTensor,x=s.webnnUploadTensor;if(!S||!x)throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');let _=await S(o,g,c);x(_,new Uint8Array(m.buffer,m.byteOffset,m.byteLength)),p=_}else w=m.byteLength,p=s._malloc(w),n.push(p),s.HEAPU8.set(new Uint8Array(m.buffer,m.byteOffset,w),p)}else w=m.byteLength,p=s._malloc(w),n.push(p),s.HEAPU8.set(new Uint8Array(m.buffer,m.byteOffset,w),p)}}let y=s.stackSave(),O=s.stackAlloc(4*c.length);try{c.forEach((h,P)=>s.setValue(O+P*u,h,u===4?"i32":"i64"));let m=s._OrtCreateTensor(ne(f),p,w,O,c.length,sn(d));m===0&&T(`Can't create tensor for input/output. session=${o}, index=${a}.`),t.push(m)}finally{s.stackRestore(y)}},xe=async(e,t,n,o,r,a)=>{let i=A(),s=i.PTR_SIZE,u=oe.get(e);if(!u)throw new Error(`cannot run inference. invalid session id: ${e}`);let f=u[0],c=u[1],l=u[2],d=u[3],p=u[4],w=u[5],y=t.length,O=o.length,m=0,h=[],P=[],g=[],S=[],x=i.stackSave(),_=i.stackAlloc(y*s),M=i.stackAlloc(y*s),K=i.stackAlloc(O*s),me=i.stackAlloc(O*s);try{[m,h]=Qt(a);for(let b=0;b<y;b++)await un(n[b],P,S,e,c[t[b]],t[b],p);for(let b=0;b<O;b++)await un(r[b],g,S,e,l[o[b]],y+o[b],p);for(let b=0;b<y;b++)i.setValue(_+b*s,P[b],"*"),i.setValue(M+b*s,c[t[b]],"*");for(let b=0;b<O;b++)i.setValue(K+b*s,g[b],"*"),i.setValue(me+b*s,l[o[b]],"*");i.jsepOnRunStart?.(f),i.webnnOnRunStart?.(f);let k;k=await i._OrtRun(f,M,_,y,me,O,K,m),k!==0&&T("failed to call OrtRun().");let Q=[];for(let b=0;b<O;b++){let ee=Number(i.getValue(K+b*s,"*"));if(ee===g[b]){Q.push(r[b]);continue}let ut=i.stackSave(),$=i.stackAlloc(4*s),ae=!1,L,D=0;try{i._OrtGetTensorData(ee,$,$+s,$+2*s,$+3*s)!==0&&T(`Can't access output tensor data on index ${b}.`);let ze=s===4?"i32":"i64",we=Number(i.getValue($,ze));D=i.getValue($+s,"*");let ft=i.getValue($+s*2,"*"),Tn=Number(i.getValue($+s*3,ze)),G=[];for(let U=0;U<Tn;U++)G.push(Number(i.getValue(ft+U*s,ze)));i._OrtFree(ft)!==0&&T("Can't free memory for tensor dims.");let z=G.reduce((U,v)=>U*v,1);L=Ce(we);let he=d?.outputPreferredLocations[o[b]];if(L==="string"){if(he==="gpu-buffer"||he==="ml-tensor")throw new Error("String tensor is not supported on GPU.");let U=[];for(let v=0;v<z;v++){let te=i.getValue(D+v*s,"*"),ye=i.getValue(D+(v+1)*s,"*"),ct=v===z-1?void 0:ye-te;U.push(i.UTF8ToString(te,ct))}Q.push([L,G,U,"cpu"])}else if(he==="gpu-buffer"&&z>0){let U=i.jsepGetBuffer;if(!U)throw new Error('preferredLocation "gpu-buffer" is not supported without using WebGPU.');let v=U(D),te=re(we,z);if(te===void 0||!Re(L))throw new Error(`Unsupported data type: ${L}`);ae=!0,Q.push([L,G,{gpuBuffer:v,download:i.jsepCreateDownloader(v,te,L),dispose:()=>{i._OrtReleaseTensor(ee)!==0&&T("Can't release tensor.")}},"gpu-buffer"])}else if(he==="ml-tensor"&&z>0){let U=i.webnnEnsureTensor,v=i.webnnIsInt64Supported;if(!U||!v)throw new Error('preferredLocation "ml-tensor" is not supported without using WebNN.');if(re(we,z)===void 0||!Fe(L))throw new Error(`Unsupported data type: ${L}`);if(L==="int64"&&!v(e))throw new Error('preferredLocation "ml-tensor" for int64 output is not supported by current WebNN Context.');let ye=await U(e,D,we,G,!1);ae=!0,Q.push([L,G,{mlTensor:ye,download:i.webnnCreateMLTensorDownloader(D,L),dispose:()=>{i.webnnReleaseTensorId(D),i._OrtReleaseTensor(ee)}},"ml-tensor"])}else{let U=rn(L),v=new U(z);new Uint8Array(v.buffer,v.byteOffset,v.byteLength).set(i.HEAPU8.subarray(D,D+v.byteLength)),Q.push([L,G,v,"cpu"])}}finally{i.stackRestore(ut),L==="string"&&D&&i._free(D),ae||i._OrtReleaseTensor(ee),i.webnnOnRunEnd?.(f)}}return d&&!p&&(i._OrtClearBoundOutputs(d.handle)!==0&&T("Can't clear bound outputs."),oe.set(e,[f,c,l,d,p,!1])),Q}finally{i.stackRestore(x),P.forEach(k=>i._OrtReleaseTensor(k)),g.forEach(k=>i._OrtReleaseTensor(k)),S.forEach(k=>i._free(k)),m!==0&&i._OrtReleaseRunOptions(m),h.forEach(k=>i._free(k))}},Le=e=>{let t=A(),n=oe.get(e);if(!n)throw new Error("invalid session id");let o=n[0],r=t._OrtEndProfiling(o);r===0&&T("Can't get an profile file name."),t._OrtFree(r)},Be=e=>{let t=[];for(let n of e){let o=n[2];!Array.isArray(o)&&"buffer"in o&&t.push(o.buffer)}return t}});var X,N,pe,Ne,We,ke,st,it,se,ie,Hn,fn,cn,dn,ln,pn,mn,wn,at=E(()=>{"use strict";q();Qe();J();Se();X=()=>!!I.wasm.proxy&&typeof document<"u",pe=!1,Ne=!1,We=!1,it=new Map,se=(e,t)=>{let n=it.get(e);n?n.push(t):it.set(e,[t])},ie=()=>{if(pe||!Ne||We||!N)throw new Error("worker not ready")},Hn=e=>{switch(e.data.type){case"init-wasm":pe=!1,e.data.err?(We=!0,st[1](e.data.err)):(Ne=!0,st[0]()),ke&&(URL.revokeObjectURL(ke),ke=void 0);break;case"init-ep":case"copy-from":case"create":case"release":case"run":case"end-profiling":{let t=it.get(e.data.type);e.data.err?t.shift()[1](e.data.err):t.shift()[0](e.data.out);break}default:}},fn=async()=>{if(!Ne){if(pe)throw new Error("multiple calls to 'initWasm()' detected.");if(We)throw new Error("previous call to 'initWasm()' failed.");if(pe=!0,X())return new Promise((e,t)=>{N?.terminate(),Zt().then(([n,o])=>{try{N=o,N.onerror=a=>t(a),N.onmessage=Hn,st=[e,t];let r={type:"init-wasm",in:I};if(!r.in.wasm.wasmPaths&&n){let a=Ue();a&&(r.in.wasm.wasmPaths=a)}N.postMessage(r),ke=n}catch(r){t(r)}},t)});try{await Ae(I.wasm),await Oe(I),Ne=!0}catch(e){throw We=!0,e}finally{pe=!1}}},cn=async e=>{if(X())return ie(),new Promise((t,n)=>{se("init-ep",[t,n]);let o={type:"init-ep",in:{epName:e,env:I}};N.postMessage(o)});await Ie(I,e)},dn=async e=>X()?(ie(),new Promise((t,n)=>{se("copy-from",[t,n]);let o={type:"copy-from",in:{buffer:e}};N.postMessage(o,[e.buffer])})):ce(e),ln=async(e,t)=>{if(X()){if(t?.preferredOutputLocation)throw new Error('session option "preferredOutputLocation" is not supported for proxy.');return ie(),new Promise((n,o)=>{se("create",[n,o]);let r={type:"create",in:{model:e,options:{...t}}},a=[];e instanceof Uint8Array&&a.push(e.buffer),N.postMessage(r,a)})}else return Pe(e,t)},pn=async e=>{if(X())return ie(),new Promise((t,n)=>{se("release",[t,n]);let o={type:"release",in:e};N.postMessage(o)});ve(e)},mn=async(e,t,n,o,r,a)=>{if(X()){if(n.some(i=>i[3]!=="cpu"))throw new Error("input tensor on GPU is not supported for proxy.");if(r.some(i=>i))throw new Error("pre-allocated output tensor is not supported for proxy.");return ie(),new Promise((i,s)=>{se("run",[i,s]);let u=n,f={type:"run",in:{sessionId:e,inputIndices:t,inputs:u,outputIndices:o,options:a}};N.postMessage(f,Be(u))})}else return xe(e,t,n,o,r,a)},wn=async e=>{if(X())return ie(),new Promise((t,n)=>{se("end-profiling",[t,n]);let o={type:"end-profiling",in:e};N.postMessage(o)});Le(e)}});var hn,jn,$e,yn=E(()=>{"use strict";q();at();rt();Te();ot();hn=(e,t)=>{switch(e.location){case"cpu":return[e.type,e.dims,e.data,"cpu"];case"gpu-buffer":return[e.type,e.dims,{gpuBuffer:e.gpuBuffer},"gpu-buffer"];case"ml-tensor":return[e.type,e.dims,{mlTensor:e.mlTensor},"ml-tensor"];default:throw new Error(`invalid data location: ${e.location} for ${t()}`)}},jn=e=>{switch(e[3]){case"cpu":return new W(e[0],e[2],e[1]);case"gpu-buffer":{let t=e[0];if(!Re(t))throw new Error(`not supported data type: ${t} for deserializing GPU tensor`);let{gpuBuffer:n,download:o,dispose:r}=e[2];return W.fromGpuBuffer(n,{dataType:t,dims:e[1],download:o,dispose:r})}case"ml-tensor":{let t=e[0];if(!Fe(t))throw new Error(`not supported data type: ${t} for deserializing MLTensor tensor`);let{mlTensor:n,download:o,dispose:r}=e[2];return W.fromMLTensor(n,{dataType:t,dims:e[1],download:o,dispose:r})}default:throw new Error(`invalid data location: ${e[3]}`)}},$e=class{async fetchModelAndCopyToWasmMemory(t){return dn(await le(t))}async loadModel(t,n){V();let o;typeof t=="string"?o=await this.fetchModelAndCopyToWasmMemory(t):o=t,[this.sessionId,this.inputNames,this.outputNames,this.inputMetadata,this.outputMetadata]=await ln(o,n),Y()}async dispose(){return pn(this.sessionId)}async run(t,n,o){V();let r=[],a=[];Object.entries(t).forEach(d=>{let p=d[0],w=d[1],y=this.inputNames.indexOf(p);if(y===-1)throw new Error(`invalid input '${p}'`);r.push(w),a.push(y)});let i=[],s=[];Object.entries(n).forEach(d=>{let p=d[0],w=d[1],y=this.outputNames.indexOf(p);if(y===-1)throw new Error(`invalid output '${p}'`);i.push(w),s.push(y)});let u=r.map((d,p)=>hn(d,()=>`input "${this.inputNames[a[p]]}"`)),f=i.map((d,p)=>d?hn(d,()=>`output "${this.outputNames[s[p]]}"`):null),c=await mn(this.sessionId,a,u,s,f,o),l={};for(let d=0;d<c.length;d++)l[this.outputNames[s[d]]]=i[d]??jn(c[d]);return Y(),l}startProfiling(){}endProfiling(){wn(this.sessionId)}}});var gn={};Ve(gn,{OnnxruntimeWebAssemblyBackend:()=>Ge,initializeFlags:()=>bn,wasmBackend:()=>Vn});var bn,Ge,Vn,En=E(()=>{"use strict";q();at();yn();bn=()=>{(typeof I.wasm.initTimeout!="number"||I.wasm.initTimeout<0)&&(I.wasm.initTimeout=0);let e=I.wasm.simd;if(typeof e!="boolean"&&e!==void 0&&e!=="fixed"&&e!=="relaxed"&&(console.warn(`Property "env.wasm.simd" is set to unknown value "${e}". Reset it to \`false\` and ignore SIMD feature checking.`),I.wasm.simd=!1),typeof I.wasm.proxy!="boolean"&&(I.wasm.proxy=!1),typeof I.wasm.trace!="boolean"&&(I.wasm.trace=!1),typeof I.wasm.numThreads!="number"||!Number.isInteger(I.wasm.numThreads)||I.wasm.numThreads<=0)if(typeof self<"u"&&!self.crossOriginIsolated)I.wasm.numThreads=1;else{let t=typeof navigator>"u"?je("node:os").cpus().length:navigator.hardwareConcurrency;I.wasm.numThreads=Math.min(4,Math.ceil((t||1)/2))}},Ge=class{async init(t){bn(),await fn(),await cn(t)}async createInferenceSessionHandler(t,n){let o=new $e;return await o.loadModel(t,n),o}},Vn=new Ge});q();q();q();var $t="1.22.0-dev.20250409-89f8206ba4";var xo=Ke;{let e=(En(),dt(gn)).wasmBackend;ue("cpu",e,10),ue("wasm",e,10)}Object.defineProperty(I.versions,"web",{value:$t,enumerable:!0});export{vn as InferenceSession,Mt as TRACE,V as TRACE_FUNC_BEGIN,Y as TRACE_FUNC_END,W as Tensor,xo as default,I as env,ue as registerBackend};
//# sourceMappingURL=ort.wasm.min.mjs.map
