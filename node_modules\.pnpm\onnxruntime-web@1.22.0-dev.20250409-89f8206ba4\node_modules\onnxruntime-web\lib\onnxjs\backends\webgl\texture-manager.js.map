{"version": 3, "file": "texture-manager.js", "sourceRoot": "", "sources": ["texture-manager.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,iDAAoD;AAYpD;;;;;;;;;GASG;AACH,MAAa,cAAc;IAMzB,YACS,SAAuB,EACvB,cAAqC,EACrC,QAA4B,EAC3B,MAA4B;QAH7B,cAAS,GAAT,SAAS,CAAc;QACvB,mBAAc,GAAd,cAAc,CAAuB;QACrC,aAAQ,GAAR,QAAQ,CAAoB;QAC3B,WAAM,GAAN,MAAM,CAAsB;QANrB,gBAAW,GAA4D,IAAI,GAAG,EAAE,CAAC;QAQhG,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;SAChC;IACH,CAAC;IACD,uBAAuB,CACrB,QAAyB,EACzB,MAAqB,EACrB,IAAwB,EACxB,KAAoB;QAEpB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAErD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QACxF,IAAI,MAAM,CAAC,QAAQ,IAAI,KAAK,oCAA4B,EAAE;YACxD,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;SACpC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE7B,IAAI,GAAuB,CAAC;QAC5B,IAAI,aAAyC,CAAC;QAC9C,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;YAC7B,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9F,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,EAAE;gBAClB,aAAa,GAAG,EAAE,CAAC;gBACnB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;aAC5C;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3C,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,EAAG,CAAC;gBACpC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,IAAI,KAAK,oCAA4B,EAAE;oBACrC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAE,CAAC,CAAC;iBACpG;gBACD,OAAO,OAAO,CAAC;aAChB;SACF;QAED,mBAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,gCAAgC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAClG,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QAE3G,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;YAC7B,aAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,GAAI,CAAC,CAAC;SACvC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,WAAW,CAAC,EAAe,EAAE,QAAyB,EAAE,QAAiB;QACvE,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,CAAC,CAAC;SACd;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,4BAA4B,EAAE,GAAG,EAAE;YACvE,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAS,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CACrC,EAAE,CAAC,OAAO,EACV,EAAE,CAAC,KAAK,EACR,EAAE,CAAC,MAAM,EACT,QAAQ,EACR,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAC5B,QAAS,CACV,CAAC;YACF,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,gBAAgB,CAAC,EAAe,EAAE,QAAyB,EAAE,QAAiB;QAClF,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,CAAC,CAAC;SACd;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAChC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjD,OAAO,IAAI,OAAO,CAAoB,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SAChF;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAClF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACjC,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAS,CAAC;YAC9D,+CAA+C;YAC/C,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CACrC,EAAE,CAAC,OAAO,EACV,EAAE,CAAC,KAAK,EACR,EAAE,CAAC,MAAM,EACT,QAAQ,EACR,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAC5B,QAAS,CACV,CAAC;YACF,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACrD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAChC,WAAW,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;YACvD,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IACD,uBAAuB,CAAC,EAAe;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,wCAAwC,EAAE,GAAG,EAAE;YACnF,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YAClG,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;IACD,cAAc,CAAC,WAAwB,EAAE,aAAuB;QAC9D,IAAI,GAAuB,CAAC;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;YAC7B,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAClD,IAAI,GAAG,EAAE;gBACP,IAAI,aAAa,EAAE;oBACjB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBAChC;gBACD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClD,IAAI,aAAa,EAAE;oBACjB,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;wBAChB,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;wBAC/B,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC9C,IAAI,CAAC,YAAY,EAAE;4BACjB,YAAY,GAAG,EAAE,CAAC;4BAClB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;yBAC1C;wBACD,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;qBACxC;iBACF;aACF;SACF;QAED,IAAI,CAAC,GAAG,IAAI,aAAa,EAAE;YACzB,mBAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,4BAA4B,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YACxG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACnD;IACH,CAAC;IACD,YAAY,CAAC,QAAyB,EAAE,IAA2B;QACjE,QAAQ,QAAQ,EAAE;YAChB,KAAK,OAAO;gBACV,OAAO,IAAI,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,KAAK,OAAO;gBACV,OAAO,IAAI,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,KAAK,MAAM;gBACT,OAAO,IAAI,YAAY,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,KAAK,QAAQ;gBACX,OAAO,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,KAAK,QAAQ;gBACX,OAAO,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,KAAK,OAAO,CAAC;YACb,KAAK,MAAM;gBACT,OAAO,IAAI,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,KAAK,SAAS;gBACZ,OAAO,IAAI,YAAY,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE,KAAK,SAAS;gBACZ,OAAO,IAAI,YAAY,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE;gBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,mBAAmB,CAAC,CAAC;SACnE;IACH,CAAC;IACD,aAAa,CAAC,SAA0B,EAAE,IAAmC;QAC3E,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,IAAI,YAAY,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QACpE;;;;;;;;;;;;;;;;;UAiBE;IACJ,CAAC;IACD,aAAa,CAAC,SAA0B;QACtC,OAAO,OAAO,CAAC;QACf,sBAAsB;QACtB,kBAAkB;QAClB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,iBAAiB;QACjB,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,sBAAsB;QACtB,aAAa;QACb,uEAAuE;QACvE,IAAI;IACN,CAAC;IACD,mBAAmB;QACjB,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;IACvC,CAAC;CACF;AArND,wCAqNC"}