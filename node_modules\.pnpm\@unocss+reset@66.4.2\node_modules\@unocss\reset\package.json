{"name": "@unocss/reset", "type": "module", "version": "66.4.2", "description": "Collection of CSS resetting", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/reset"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": [], "exports": {"./*": "./*"}, "files": ["**/*.css"], "devDependencies": {"@csstools/normalize.css": "^12.1.1", "sanitize.css": "^13.0.0"}, "scripts": {"build": "esno scripts/copy.ts"}}