{"version": 3, "file": "image-scaler.js", "sourceRoot": "", "sources": ["image-scaler.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAKvG,oCAAwF;AAOjF,MAAM,WAAW,GAAkD,CACxE,gBAAuC,EACvC,MAAgB,EAChB,UAAiC,EACvB,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IACvB,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,kCAAkC,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;IACtH,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AARW,QAAA,WAAW,eAQtB;AAEK,MAAM,0BAA0B,GAAkD,CACvF,IAAgB,EACO,EAAE;IACzB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/C,OAAO,IAAA,sDAA2B,EAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AACtD,CAAC,CAAC;AANW,QAAA,0BAA0B,8BAMrC;AAEF,MAAM,0BAA0B,GAAG;IACjC,IAAI,EAAE,aAAa;IACnB,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEF,MAAM,4BAA4B,GAAG,CACnC,QAA+B,EAC/B,QAAyB,EACzB,MAAgB,EAChB,UAAiC,EACpB,EAAE;IACf,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,aAAa,GAAG,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClE,MAAM,YAAY,GAAG;QACf,aAAa;kCACa,IAAI;;QAE9B,CAAC;IACP,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,SAAS,EAAE;YACT,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;YAC3F,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE;SACzD;QACD,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,kCAAkC,GAAG,CACzC,OAA8B,EAC9B,MAAgB,EAChB,UAAiC,EACd,EAAE;IACrB,MAAM,QAAQ,GAAG,EAAE,GAAG,0BAA0B,EAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC;IACnF,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,4BAA4B,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;AACzG,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,WAAmB,EAAU,EAAE;IAC1D,MAAM,SAAS,GAAa,CAAC,4BAA4B,WAAW,mBAAmB,CAAC,CAAC;IACzF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,KAAK,CAAC,EAAE;YACX,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACtE;aAAM,IAAI,CAAC,KAAK,WAAW,GAAG,CAAC,EAAE;YAChC,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;SACtD;aAAM;YACL,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SAC3E;KACF;IACD,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IAC3B,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;KAClD;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC"}