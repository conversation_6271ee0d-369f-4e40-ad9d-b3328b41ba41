{"version": 3, "file": "gemm.js", "sourceRoot": "", "sources": ["gemm.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAIvG,wCAAyC;AAEzC,oCAAwF;AAUjF,MAAM,IAAI,GAA2C,CAC1D,gBAAuC,EACvC,MAAgB,EAChB,UAA0B,EAChB,EAAE;IACZ,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IACnC,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,2BAA2B,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;IAC7F,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AARW,QAAA,IAAI,QAQf;AAEF,MAAM,mBAAmB,GAAG,CAAC,IAAgB,EAAE,WAAoB,EAAkB,EAAE;IACrF,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACzD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACzD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACrD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACnD,OAAO,IAAA,sDAA2B,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;AACnF,CAAC,CAAC;AAEK,MAAM,qBAAqB,GAA2C,CAAC,IAAgB,EAAkB,EAAE,CAChH,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AADtB,QAAA,qBAAqB,yBACC;AAE5B,MAAM,sBAAsB,GAA2C,CAAC,IAAgB,EAAkB,EAAE,CACjH,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AADrB,QAAA,sBAAsB,0BACD;AAElC,MAAM,2BAA2B,GAAG,CAAC,MAAgB,EAAE,UAA0B,EAAqB,EAAE;IACtG,MAAM,QAAQ,GAAG;QACf,IAAI,EAAE,MAAM;QACZ,UAAU,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAC9D,UAAU,EACR,MAAM,CAAC,MAAM,KAAK,CAAC;YACjB,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;YACpE,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;QAClD,GAAG,EAAE,UAAU,CAAC,QAAQ;KACzB,CAAC;IAEF,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;AACzF,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,CAC5B,QAAyB,EACzB,MAAgB,EAChB,UAA0B,EACb,EAAE;IACf,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACtC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACtC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,eAAQ,CAAC,oBAAoB,CAC1C,MAAM,EACN,UAAU,CAAC,MAAM,EACjB,MAAM,EACN,UAAU,CAAC,MAAM,EACjB,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CACjD,CAAC;IACF,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;KACxD;IACD,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACvB;IACD,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE;QAC1C,IAAI,GAAG,6BAA6B,CAAC;KACtC;SAAM,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QAClD,IAAI,GAAG,2BAA2B,CAAC;KACpC;SAAM,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE;QAClD,IAAI,GAAG,2BAA2B,CAAC;KACpC;SAAM,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACnD,IAAI,GAAG,yBAAyB,CAAC;KAClC;IACD,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/E,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5E,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,MAAM,YAAY,GAAG;kCACW,IAAI;kBACpB,IAAI;kBACJ,IAAI;YACV,QAAQ;;;;YAIR,UAAU;;;4BAGM,SAAS;kBACnB,IAAI,GAAG,CAAC;kBACR,IAAI,GAAG,CAAC;gBACV,IAAI;;;;YAIR,UAAU;;QAEd,CAAC;IACP,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,SAAS,EAAE;YACT,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE;YACxD,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;SACvD;QACD,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAE,UAA0B,EAAQ,EAAE;IAC5E,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACrC;IACD,IAAI,UAAU,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;QACtE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;IACD,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;KAC3C;IAED,2CAA2C;IAC3C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrF,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;KAC7C;IAED,IACE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC;QAC9D,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC;QAC9D,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,EACrF;QACA,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QACnG,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;AACH,CAAC,CAAC"}