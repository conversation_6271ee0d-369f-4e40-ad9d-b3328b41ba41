import { CommonRequestOptions } from '@xsai/shared';

type CreateProviderOptions = Omit<CommonRequestOptions, 'model'>;

interface ProviderMetadata {
    id: string;
}

interface ChatProvider<T = string> {
    chat: (model: (string & {}) | T) => CommonRequestOptions;
}
interface ChatProviderWithExtraOptions<T = string, T2 = undefined> {
    chat: (model: (string & {}) | T, extraOptions?: T2) => CommonRequestOptions & Partial<T2>;
}
interface EmbedProvider<T = string> {
    embed: (model: (string & {}) | T) => CommonRequestOptions;
}
interface EmbedProviderWithExtraOptions<T = string, T2 = undefined> {
    embed: (model: (string & {}) | T, extraOptions?: T2) => CommonRequestOptions & Partial<T2>;
}
interface ImageProvider<T = string> {
    image: (model: (string & {}) | T) => CommonRequestOptions;
}
interface MetadataProviders {
    metadata: ProviderMetadata;
}
interface ModelProvider {
    model: () => Omit<CommonRequestOptions, 'model'>;
}
interface ModelProviderWithExtraOptions<T = undefined> {
    model: (options?: T) => Omit<CommonRequestOptions, 'model'> & Partial<T>;
}
interface SpeechProvider<T = string> {
    speech: (model: (string & {}) | T) => CommonRequestOptions;
}
interface SpeechProviderWithExtraOptions<T = string, T2 = undefined> {
    speech: (model: (string & {}) | T, extraOptions?: T2) => CommonRequestOptions & Partial<T2>;
}
interface TranscriptionProvider<T = string> {
    transcription: (model: (string & {}) | T) => CommonRequestOptions;
}
interface TranscriptionProviderWithExtraOptions<T = string, T2 = undefined> {
    transcription: (model: (string & {}) | T, extraOptions?: T2) => CommonRequestOptions & Partial<T2>;
}

declare const createChatProvider: <T extends string = string>(options: CreateProviderOptions) => ChatProvider<T>;
declare const createChatProviderWithExtraOptions: <T extends string = string, T2 = undefined>(options: CreateProviderOptions) => ChatProviderWithExtraOptions<T, T2>;
declare const createEmbedProvider: <T extends string = string>(options: CreateProviderOptions) => EmbedProvider<T>;
declare const createEmbedProviderWithExtraOptions: <T extends string = string, T2 = undefined>(options: CreateProviderOptions) => EmbedProviderWithExtraOptions<T, T2>;
declare const createImageProvider: <T extends string = string>(options: CreateProviderOptions) => ImageProvider<T>;
declare const createModelProvider: (options: CreateProviderOptions) => ModelProvider;
declare const createModelProviderWithExtraOptions: <T = undefined>(options: Omit<CreateProviderOptions, "model">) => ModelProviderWithExtraOptions<T>;
declare const createSpeechProvider: <T extends string = string>(options: CreateProviderOptions) => SpeechProvider<T>;
declare const createSpeechProviderWithExtraOptions: <T extends string = string, T2 = undefined>(options: CreateProviderOptions) => SpeechProviderWithExtraOptions<T, T2>;
declare const createTranscriptionProvider: <T extends string = string>(options: CreateProviderOptions) => TranscriptionProvider<T>;
declare const createTranscriptionProviderWithExtraOptions: <T extends string = string, T2 = undefined>(options: CreateProviderOptions) => TranscriptionProviderWithExtraOptions<T, T2>;
declare const createMetadataProvider: (id: string, otherMeta?: Omit<ProviderMetadata, "id">) => MetadataProviders;

declare global {
	// eslint-disable-next-line @typescript-eslint/consistent-type-definitions -- It has to be an `interface` so that it can be merged.
	interface SymbolConstructor {
		readonly observable: symbol;
	}
}

/**
Convert a union type to an intersection type using [distributive conditional types](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-8.html#distributive-conditional-types).

Inspired by [this Stack Overflow answer](https://stackoverflow.com/a/50375286/2172153).

@example
```
import type {UnionToIntersection} from 'type-fest';

type Union = {the(): void} | {great(arg: string): void} | {escape: boolean};

type Intersection = UnionToIntersection<Union>;
//=> {the(): void; great(arg: string): void; escape: boolean};
```

A more applicable example which could make its way into your library code follows.

@example
```
import type {UnionToIntersection} from 'type-fest';

class CommandOne {
	commands: {
		a1: () => undefined,
		b1: () => undefined,
	}
}

class CommandTwo {
	commands: {
		a2: (argA: string) => undefined,
		b2: (argB: string) => undefined,
	}
}

const union = [new CommandOne(), new CommandTwo()].map(instance => instance.commands);
type Union = typeof union;
//=> {a1(): void; b1(): void} | {a2(argA: string): void; b2(argB: string): void}

type Intersection = UnionToIntersection<Union>;
//=> {a1(): void; b1(): void; a2(argA: string): void; b2(argB: string): void}
```

@category Type
*/
type UnionToIntersection<Union> = (
	// `extends unknown` is always going to be the case and is used to convert the
	// `Union` into a [distributive conditional
	// type](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-8.html#distributive-conditional-types).
	Union extends unknown
		// The union type is used as the only argument to a function since the union
		// of function arguments is an intersection.
		? (distributedUnion: Union) => void
		// This won't happen.
		: never
		// Infer the `Intersection` type since TypeScript represents the positional
		// arguments of unions of functions as an intersection of the union.
) extends ((mergedIntersection: infer Intersection) => void)
	// The `& Union` is to allow indexing by the resulting type
	? Intersection & Union
	: never;

declare const merge: <T extends object[]>(...arr: T) => UnionToIntersection<T[number]>;

export { type ChatProvider, type ChatProviderWithExtraOptions, type CreateProviderOptions, type EmbedProvider, type EmbedProviderWithExtraOptions, type ImageProvider, type MetadataProviders, type ModelProvider, type ModelProviderWithExtraOptions, type ProviderMetadata, type SpeechProvider, type SpeechProviderWithExtraOptions, type TranscriptionProvider, type TranscriptionProviderWithExtraOptions, createChatProvider, createChatProviderWithExtraOptions, createEmbedProvider, createEmbedProviderWithExtraOptions, createImageProvider, createMetadataProvider, createModelProvider, createModelProviderWithExtraOptions, createSpeechProvider, createSpeechProviderWithExtraOptions, createTranscriptionProvider, createTranscriptionProviderWithExtraOptions, merge };
