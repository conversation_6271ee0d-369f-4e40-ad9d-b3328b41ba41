{"version": 3, "file": "nodes-to-optimize-indices.js", "sourceRoot": "", "sources": ["nodes-to-optimize-indices.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;AAErE,oIAAoI;AAEpI,yDAA2C;AAE3C;;;GAGG;AACH,MAAa,sBAAsB;IAAnC;QACE,OAAE,GAAkC,IAAI,CAAC;QACzC,WAAM,GAAG,CAAC,CAAC;IAsJb,CAAC;IArJC,MAAM,CAAC,CAAS,EAAE,EAA0B;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,+BAA+B,CACpC,EAA0B,EAC1B,GAA4B;QAE5B,OAAO,CAAC,GAAG,IAAI,IAAI,sBAAsB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACvG,CAAC;IAED,MAAM,CAAC,2CAA2C,CAChD,EAA0B,EAC1B,GAA4B;QAE5B,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,sBAAsB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACvG,CAAC;IAED,WAAW,CAAC,KAAa;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/F,CAAC;IAED,iBAAiB;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,gBAAgB;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM;YACX,CAAC,CAAC,IAAI,WAAW,CACb,IAAI,CAAC,EAAG,CAAC,KAAK,EAAE,CAAC,MAAM,EACvB,IAAI,CAAC,EAAG,CAAC,KAAK,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EACrE,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAC5C;YACH,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED,SAAS;QACP,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,UAAU;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,gBAAgB;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,iBAAiB;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,iBAAiB;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,kBAAkB;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,CAAC,2BAA2B,CAAC,OAA4B;QAC7D,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAA4B,EAAE,iBAAqC;QACvF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAOD,MAAM,CAAC,uBAAuB,CAC5B,OAA4B,EAC5B,IAAyC;QAEzC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;SAC5B;QACD,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,OAA4B,EAAE,QAAgB;QAC1E,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA4B,EAAE,SAAiB;QACjE,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAA4B,EAAE,UAAkB;QACnE,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,OAA4B,EAAE,gBAAyB;QAChF,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,KAAK,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAA4B,EAAE,iBAA0B;QAClF,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAA4B,EAAE,iBAAyB;QACjF,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,OAA4B,EAAE,kBAA0B;QACnF,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,OAA4B;QAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,4BAA4B,CACjC,OAA4B,EAC5B,iBAAqC,EACrC,SAAiB,EACjB,UAAkB,EAClB,gBAAyB,EACzB,iBAA0B,EAC1B,iBAAyB,EACzB,kBAA0B;QAE1B,sBAAsB,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAC5D,sBAAsB,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAClE,sBAAsB,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACxD,sBAAsB,CAAC,aAAa,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1D,sBAAsB,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACtE,sBAAsB,CAAC,oBAAoB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACxE,sBAAsB,CAAC,oBAAoB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACxE,sBAAsB,CAAC,qBAAqB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC1E,OAAO,sBAAsB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;IACnE,CAAC;CACF;AAxJD,wDAwJC"}