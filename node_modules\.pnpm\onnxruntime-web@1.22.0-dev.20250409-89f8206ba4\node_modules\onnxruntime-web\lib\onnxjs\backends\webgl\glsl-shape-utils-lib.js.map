{"version": 3, "file": "glsl-shape-utils-lib.js", "sourceRoot": "", "sources": ["glsl-shape-utils-lib.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,yDAA0E;AAE1E;;;GAGG;AACH,MAAa,iBAAkB,SAAQ,0BAAO;IAC5C,YAAY,OAAoB;QAC9B,KAAK,CAAC,OAAO,CAAC,CAAC;IACjB,CAAC;IACD,YAAY;QACV,OAAO;YACL,GAAG,IAAI,CAAC,UAAU,EAAE;YACpB,GAAG,IAAI,CAAC,gBAAgB,EAAE;YAC1B,GAAG,IAAI,CAAC,eAAe,EAAE;YACzB,GAAG,IAAI,CAAC,eAAe,EAAE;YACzB,GAAG,IAAI,CAAC,gBAAgB,EAAE;SAC3B,CAAC;IACJ,CAAC;IACD,cAAc;QACZ,OAAO,EAAE,CAAC;IACZ,CAAC;IACS,UAAU;QAClB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC;QACjE,MAAM,MAAM,GAAuC,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;YAChE,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,EAAE;gBAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC1B,MAAM,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC;gBACpC,MAAM,QAAQ,GAAG,gBAAgB,IAAI,EAAE,CAAC;gBACxC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;oBAC7B,KAAK,IAAI;wBACK,CAAC,qCAAqC,SAAS,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC;WAC/E,CAAC;iBACH;gBACD,MAAM,IAAI,GAAG;eACN,QAAQ,wBAAwB,UAAU,0BAA0B,IAAI;YAC3E,KAAK;;SAER,CAAC;gBACF,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CAAC,IAAI,CAAC,CAAC;aAC7C;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACS,gBAAgB;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC;QACjE,MAAM,MAAM,GAAuC,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE;gBACpD,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC1B,MAAM,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC;gBACpC,MAAM,QAAQ,GAAG,sBAAsB,IAAI,EAAE,CAAC;gBAC9C,IAAI,KAAK,GAAG,EAAE,CAAC;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACjC,KAAK,IAAI;wBACK,CAAC,qCAAqC,SAAS,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC;WAC/E,CAAC;iBACH;gBACD,MAAM,IAAI,GAAG;eACN,QAAQ,uBAAuB,UAAU,0BAA0B,IAAI;YAC1E,KAAK;wBACO,IAAI,GAAG,CAAC,sBAAsB,UAAU,GAAG,CAAC;wBAC5C,IAAI,GAAG,CAAC,sBAAsB,UAAU,GAAG,CAAC;;SAE3D,CAAC;gBACF,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CAAC,IAAI,CAAC,CAAC;aAC7C;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACS,eAAe;QACvB,MAAM,MAAM,GAAuC,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5D,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,IAAI,QAAQ,GAAG,mBAAmB,IAAI,EAAE,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YACtG,QAAQ,GAAG,mBAAmB,IAAI,IAAI,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CACnC,iBAAiB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CACjF,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,MAAM,CAAC,mBAAmB,CAAC,IAAY,EAAE,IAAY,EAAE,OAA0B;QAC/E,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;YAClC,KAAK,IAAI;4BACa,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC;SACrC,CAAC;SACL;QACD,OAAO;YACC,IAAI,gBAAgB,IAAI;;UAE1B,KAAK;;;OAGR,CAAC;IACN,CAAC;IACS,eAAe;QACvB,MAAM,MAAM,GAAuC,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5D,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,IAAI,QAAQ,GAAG,mBAAmB,IAAI,EAAE,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YACxG,QAAQ,GAAG,mBAAmB,IAAI,IAAI,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CACnC,iBAAiB,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CACnF,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,MAAM,CAAC,qBAAqB,CAAC,IAAY,EAAE,IAAY,EAAE,OAA0B;QACjF,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YACjC,YAAY,CAAC,IAAI,CAAC;gBACR,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C,YAAY,CAAC,IAAI,CAAC;4BACI,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC9C;QACD,YAAY,CAAC,IAAI,CAAC;gBACN,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;QACnC,OAAO;aACE,IAAI,gCAAgC,IAAI;UAC3C,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;;OAExB,CAAC;IACN,CAAC;IACS,gBAAgB;QACxB,MAAM,MAAM,GAAuC,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,MAAM,QAAQ,GAAG,oBAAoB,IAAI,EAAE,CAAC;YAC5C,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;gBAC7B,SAAS,IAAI;gBACL,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;aAC7B;YACD,MAAM,IAAI,GAAG;eACJ,QAAQ,8BAA8B,IAAI;sBACnC,IAAI;YACd,SAAS;wBACG,IAAI;;;;;;;;;SASnB,CAAC;YACJ,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,iCAAc,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA9JD,8CA8JC"}