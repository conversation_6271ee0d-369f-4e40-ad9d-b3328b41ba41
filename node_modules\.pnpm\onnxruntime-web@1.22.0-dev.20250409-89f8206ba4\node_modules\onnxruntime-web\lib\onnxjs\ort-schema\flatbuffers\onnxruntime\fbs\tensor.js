'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.Tensor = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const tensor_data_type_js_1 = require('../../onnxruntime/fbs/tensor-data-type.js');
class Tensor {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsTensor(bb, obj) {
    return (obj || new Tensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsTensor(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new Tensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  name(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  docString(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  dims(index) {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.readInt64(this.bb.__vector(this.bb_pos + offset) + index * 8) : BigInt(0);
  }
  dimsLength() {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  dataType() {
    const offset = this.bb.__offset(this.bb_pos, 10);
    return offset ? this.bb.readInt32(this.bb_pos + offset) : tensor_data_type_js_1.TensorDataType.UNDEFINED;
  }
  rawData(index) {
    const offset = this.bb.__offset(this.bb_pos, 12);
    return offset ? this.bb.readUint8(this.bb.__vector(this.bb_pos + offset) + index) : 0;
  }
  rawDataLength() {
    const offset = this.bb.__offset(this.bb_pos, 12);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  rawDataArray() {
    const offset = this.bb.__offset(this.bb_pos, 12);
    return offset
      ? new Uint8Array(
          this.bb.bytes().buffer,
          this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset),
          this.bb.__vector_len(this.bb_pos + offset),
        )
      : null;
  }
  stringData(index, optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 14);
    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
  }
  stringDataLength() {
    const offset = this.bb.__offset(this.bb_pos, 14);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  externalDataOffset() {
    const offset = this.bb.__offset(this.bb_pos, 16);
    return offset ? this.bb.readInt64(this.bb_pos + offset) : BigInt('-1');
  }
  static startTensor(builder) {
    builder.startObject(7);
  }
  static addName(builder, nameOffset) {
    builder.addFieldOffset(0, nameOffset, 0);
  }
  static addDocString(builder, docStringOffset) {
    builder.addFieldOffset(1, docStringOffset, 0);
  }
  static addDims(builder, dimsOffset) {
    builder.addFieldOffset(2, dimsOffset, 0);
  }
  static createDimsVector(builder, data) {
    builder.startVector(8, data.length, 8);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt64(data[i]);
    }
    return builder.endVector();
  }
  static startDimsVector(builder, numElems) {
    builder.startVector(8, numElems, 8);
  }
  static addDataType(builder, dataType) {
    builder.addFieldInt32(3, dataType, tensor_data_type_js_1.TensorDataType.UNDEFINED);
  }
  static addRawData(builder, rawDataOffset) {
    builder.addFieldOffset(4, rawDataOffset, 0);
  }
  static createRawDataVector(builder, data) {
    builder.startVector(1, data.length, 1);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt8(data[i]);
    }
    return builder.endVector();
  }
  static startRawDataVector(builder, numElems) {
    builder.startVector(1, numElems, 1);
  }
  static addStringData(builder, stringDataOffset) {
    builder.addFieldOffset(5, stringDataOffset, 0);
  }
  static createStringDataVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startStringDataVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static addExternalDataOffset(builder, externalDataOffset) {
    builder.addFieldInt64(6, externalDataOffset, BigInt('-1'));
  }
  static endTensor(builder) {
    const offset = builder.endObject();
    return offset;
  }
  static createTensor(
    builder,
    nameOffset,
    docStringOffset,
    dimsOffset,
    dataType,
    rawDataOffset,
    stringDataOffset,
    externalDataOffset,
  ) {
    Tensor.startTensor(builder);
    Tensor.addName(builder, nameOffset);
    Tensor.addDocString(builder, docStringOffset);
    Tensor.addDims(builder, dimsOffset);
    Tensor.addDataType(builder, dataType);
    Tensor.addRawData(builder, rawDataOffset);
    Tensor.addStringData(builder, stringDataOffset);
    Tensor.addExternalDataOffset(builder, externalDataOffset);
    return Tensor.endTensor(builder);
  }
}
exports.Tensor = Tensor;
//# sourceMappingURL=tensor.js.map
