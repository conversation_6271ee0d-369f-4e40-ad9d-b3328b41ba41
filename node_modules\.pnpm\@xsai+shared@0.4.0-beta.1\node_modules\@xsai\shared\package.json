{"name": "@xsai/shared", "type": "module", "version": "0.4.0-beta.1", "description": "extra-small AI SDK.", "author": "Moeru AI", "license": "MIT", "homepage": "https://xsai.js.org", "repository": {"type": "git", "url": "git+https://github.com/moeru-ai/xsai.git", "directory": "packages/shared"}, "bugs": "https://github.com/moeru-ai/xsai/issues", "keywords": ["xsai", "openai", "ai"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "devDependencies": {"@moeru/std": "^0.1.0-beta.7"}, "scripts": {"build": "pkgroll"}, "main": "./dist/index.js", "types": "./dist/index.d.ts"}