'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.Node = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const attribute_js_1 = require('../../onnxruntime/fbs/attribute.js');
const node_type_js_1 = require('../../onnxruntime/fbs/node-type.js');
class Node {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsNode(bb, obj) {
    return (obj || new Node()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsNode(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new Node()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  name(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  docString(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  domain(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 8);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  sinceVersion() {
    const offset = this.bb.__offset(this.bb_pos, 10);
    return offset ? this.bb.readInt32(this.bb_pos + offset) : 0;
  }
  index() {
    const offset = this.bb.__offset(this.bb_pos, 12);
    return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
  }
  opType(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 14);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  type() {
    const offset = this.bb.__offset(this.bb_pos, 16);
    return offset ? this.bb.readInt32(this.bb_pos + offset) : node_type_js_1.NodeType.Primitive;
  }
  executionProviderType(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 18);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  inputs(index, optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 20);
    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
  }
  inputsLength() {
    const offset = this.bb.__offset(this.bb_pos, 20);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  outputs(index, optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 22);
    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
  }
  outputsLength() {
    const offset = this.bb.__offset(this.bb_pos, 22);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  attributes(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 24);
    return offset
      ? (obj || new attribute_js_1.Attribute()).__init(
          this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4),
          this.bb,
        )
      : null;
  }
  attributesLength() {
    const offset = this.bb.__offset(this.bb_pos, 24);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  inputArgCounts(index) {
    const offset = this.bb.__offset(this.bb_pos, 26);
    return offset ? this.bb.readInt32(this.bb.__vector(this.bb_pos + offset) + index * 4) : 0;
  }
  inputArgCountsLength() {
    const offset = this.bb.__offset(this.bb_pos, 26);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  inputArgCountsArray() {
    const offset = this.bb.__offset(this.bb_pos, 26);
    return offset
      ? new Int32Array(
          this.bb.bytes().buffer,
          this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset),
          this.bb.__vector_len(this.bb_pos + offset),
        )
      : null;
  }
  implicitInputs(index, optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 28);
    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
  }
  implicitInputsLength() {
    const offset = this.bb.__offset(this.bb_pos, 28);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startNode(builder) {
    builder.startObject(13);
  }
  static addName(builder, nameOffset) {
    builder.addFieldOffset(0, nameOffset, 0);
  }
  static addDocString(builder, docStringOffset) {
    builder.addFieldOffset(1, docStringOffset, 0);
  }
  static addDomain(builder, domainOffset) {
    builder.addFieldOffset(2, domainOffset, 0);
  }
  static addSinceVersion(builder, sinceVersion) {
    builder.addFieldInt32(3, sinceVersion, 0);
  }
  static addIndex(builder, index) {
    builder.addFieldInt32(4, index, 0);
  }
  static addOpType(builder, opTypeOffset) {
    builder.addFieldOffset(5, opTypeOffset, 0);
  }
  static addType(builder, type) {
    builder.addFieldInt32(6, type, node_type_js_1.NodeType.Primitive);
  }
  static addExecutionProviderType(builder, executionProviderTypeOffset) {
    builder.addFieldOffset(7, executionProviderTypeOffset, 0);
  }
  static addInputs(builder, inputsOffset) {
    builder.addFieldOffset(8, inputsOffset, 0);
  }
  static createInputsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startInputsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static addOutputs(builder, outputsOffset) {
    builder.addFieldOffset(9, outputsOffset, 0);
  }
  static createOutputsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startOutputsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static addAttributes(builder, attributesOffset) {
    builder.addFieldOffset(10, attributesOffset, 0);
  }
  static createAttributesVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startAttributesVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static addInputArgCounts(builder, inputArgCountsOffset) {
    builder.addFieldOffset(11, inputArgCountsOffset, 0);
  }
  static createInputArgCountsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt32(data[i]);
    }
    return builder.endVector();
  }
  static startInputArgCountsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static addImplicitInputs(builder, implicitInputsOffset) {
    builder.addFieldOffset(12, implicitInputsOffset, 0);
  }
  static createImplicitInputsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startImplicitInputsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static endNode(builder) {
    const offset = builder.endObject();
    return offset;
  }
  static createNode(
    builder,
    nameOffset,
    docStringOffset,
    domainOffset,
    sinceVersion,
    index,
    opTypeOffset,
    type,
    executionProviderTypeOffset,
    inputsOffset,
    outputsOffset,
    attributesOffset,
    inputArgCountsOffset,
    implicitInputsOffset,
  ) {
    Node.startNode(builder);
    Node.addName(builder, nameOffset);
    Node.addDocString(builder, docStringOffset);
    Node.addDomain(builder, domainOffset);
    Node.addSinceVersion(builder, sinceVersion);
    Node.addIndex(builder, index);
    Node.addOpType(builder, opTypeOffset);
    Node.addType(builder, type);
    Node.addExecutionProviderType(builder, executionProviderTypeOffset);
    Node.addInputs(builder, inputsOffset);
    Node.addOutputs(builder, outputsOffset);
    Node.addAttributes(builder, attributesOffset);
    Node.addInputArgCounts(builder, inputArgCountsOffset);
    Node.addImplicitInputs(builder, implicitInputsOffset);
    return Node.endNode(builder);
  }
}
exports.Node = Node;
//# sourceMappingURL=node.js.map
