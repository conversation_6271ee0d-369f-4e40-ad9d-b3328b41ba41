{"name": "@xsai/shared", "type": "module", "version": "0.2.2", "description": "extra-small AI SDK.", "author": "Moeru AI", "license": "MIT", "homepage": "https://xsai.js.org", "repository": {"type": "git", "url": "git+https://github.com/moeru-ai/xsai.git", "directory": "packages/shared"}, "bugs": "https://github.com/moeru-ai/xsai/issues", "keywords": ["xsai", "openai", "ai"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"build": "pkgroll"}, "main": "./dist/index.js", "types": "./dist/index.d.ts"}