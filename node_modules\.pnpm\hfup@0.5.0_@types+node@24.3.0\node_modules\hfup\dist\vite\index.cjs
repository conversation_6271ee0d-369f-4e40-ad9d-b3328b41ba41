"use strict";
//#region rolldown:runtime
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
	if (from && typeof from === "object" || typeof from === "function") for (var keys = __getOwnPropNames(from), i = 0, n = keys.length, key; i < n; i++) {
		key = keys[i];
		if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
			get: ((k) => from[k]).bind(null, key),
			enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
		});
	}
	return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
	value: mod,
	enumerable: true
}) : target, mod));

//#endregion
const node_fs_promises = __toESM(require("node:fs/promises"));
const node_path = __toESM(require("node:path"));
const defu = __toESM(require("defu"));
const gray_matter = __toESM(require("gray-matter"));

//#region src/vite/lfs.ts
const defaultGitAttributes = `# Default
*.7z filter=lfs diff=lfs merge=lfs -text
*.arrow filter=lfs diff=lfs merge=lfs -text
*.bin filter=lfs diff=lfs merge=lfs -text
*.bz2 filter=lfs diff=lfs merge=lfs -text
*.ckpt filter=lfs diff=lfs merge=lfs -text
*.ftz filter=lfs diff=lfs merge=lfs -text
*.gz filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.joblib filter=lfs diff=lfs merge=lfs -text
*.lfs.* filter=lfs diff=lfs merge=lfs -text
*.mlmodel filter=lfs diff=lfs merge=lfs -text
*.model filter=lfs diff=lfs merge=lfs -text
*.msgpack filter=lfs diff=lfs merge=lfs -text
*.npy filter=lfs diff=lfs merge=lfs -text
*.npz filter=lfs diff=lfs merge=lfs -text
*.onnx filter=lfs diff=lfs merge=lfs -text
*.ot filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
*.pb filter=lfs diff=lfs merge=lfs -text
*.pickle filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.pt filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.safetensors filter=lfs diff=lfs merge=lfs -text
saved_model/**/* filter=lfs diff=lfs merge=lfs -text
*.tar.* filter=lfs diff=lfs merge=lfs -text
*.tar filter=lfs diff=lfs merge=lfs -text
*.tflite filter=lfs diff=lfs merge=lfs -text
*.tgz filter=lfs diff=lfs merge=lfs -text
*.wasm filter=lfs diff=lfs merge=lfs -text
*.xz filter=lfs diff=lfs merge=lfs -text
*.zip filter=lfs diff=lfs merge=lfs -text
*.zst filter=lfs diff=lfs merge=lfs -text
*tfevents* filter=lfs diff=lfs merge=lfs -text
`;
function LFS(options) {
	let _config;
	return {
		name: "huggingspace:lfs-gitattributes",
		configResolved(config) {
			_config = config;
		},
		async generateBundle() {
			if (options?.enableFn && !await options.enableFn(_config)) return;
			const extraGlobs = options?.extraGlobs ?? [];
			const extraAttributes = options?.extraAttributes ?? [];
			const withDefault = options?.withDefault ?? true;
			const gitAttributes = withDefault ? defaultGitAttributes : "";
			const extraGlobsIntoGitAttributes = extraGlobs.map((glob) => {
				return `${glob} filter=lfs diff=lfs merge=lfs -text`;
			});
			this.emitFile({
				type: "asset",
				fileName: ".gitattributes",
				source: `${extraAttributes.join("\n")}${extraGlobsIntoGitAttributes.join("\n")}\n${gitAttributes}`
			});
		}
	};
}

//#endregion
//#region src/utils/fs.ts
async function exists(path) {
	try {
		await (0, node_fs_promises.stat)(path);
		return true;
	} catch (error) {
		if (isENOENTError(error)) return false;
		throw error;
	}
}
function isENOENTError(error) {
	if (!(error instanceof Error)) return false;
	if (!("code" in error)) return false;
	if (error.code !== "ENOENT") return false;
	return true;
}

//#endregion
//#region src/vite/space-card/types.ts
const licenseValues = [
	"apache-2.0",
	"mit",
	"openrail",
	"bigscience-openrail-m",
	"creativeml-openrail-m",
	"bigscience-bloom-rail-1.0",
	"bigcode-openrail-m",
	"afl-3.0",
	"artistic-2.0",
	"bsl-1.0",
	"bsd",
	"bsd-2-clause",
	"bsd-3-clause",
	"bsd-3-clause-clear",
	"c-uda",
	"cc",
	"cc0-1.0",
	"cc-by-2.0",
	"cc-by-2.5",
	"cc-by-3.0",
	"cc-by-4.0",
	"cc-by-sa-3.0",
	"cc-by-sa-4.0",
	"cc-by-nc-2.0",
	"cc-by-nc-3.0",
	"cc-by-nc-4.0",
	"cc-by-nd-4.0",
	"cc-by-nc-nd-3.0",
	"cc-by-nc-nd-4.0",
	"cc-by-nc-sa-2.0",
	"cc-by-nc-sa-3.0",
	"cc-by-nc-sa-4.0",
	"cdla-sharing-1.0",
	"cdla-permissive-1.0",
	"cdla-permissive-2.0",
	"wtfpl",
	"ecl-2.0",
	"epl-1.0",
	"epl-2.0",
	"etalab-2.0",
	"eupl-1.1",
	"agpl-3.0",
	"gfdl",
	"gpl",
	"gpl-2.0",
	"gpl-3.0",
	"lgpl",
	"lgpl-2.1",
	"lgpl-3.0",
	"isc",
	"lppl-1.3c",
	"ms-pl",
	"apple-ascl",
	"mpl-2.0",
	"odc-by",
	"odbl",
	"openrail++",
	"osl-3.0",
	"postgresql",
	"ofl-1.1",
	"ncsa",
	"unlicense",
	"zlib",
	"pddl",
	"lgpl-lr",
	"deepfloyd-if-license",
	"llama2",
	"llama3",
	"llama3.1",
	"llama3.2",
	"llama3.3",
	"gemma",
	"unknown",
	"other"
];

//#endregion
//#region src/vite/space-card/index.ts
function SpaceCard(configuration) {
	let _config;
	const _configuration = (0, defu.defu)(configuration, {
		emoji: "🚀",
		sdk: "static",
		pinned: false,
		license: "unknown"
	});
	let packageJSON = {};
	let readme = "";
	return {
		name: "huggingspace:readme",
		async configResolved(config) {
			_config = config;
			const rootPackageJSONPath = (0, node_path.join)(_config.root, "package.json");
			if (await exists(rootPackageJSONPath)) {
				const rootPackageJSONContent = await (0, node_fs_promises.readFile)(rootPackageJSONPath, "utf-8");
				packageJSON = JSON.parse(rootPackageJSONContent);
			}
			const rootReadmePaths = [
				(0, node_path.join)(_config.root, "README.md"),
				(0, node_path.join)(_config.root, "readme.md"),
				(0, node_path.join)(_config.root, "README.markdown"),
				(0, node_path.join)(_config.root, "readme.markdown"),
				(0, node_path.join)(_config.root, "README"),
				(0, node_path.join)(_config.root, "readme")
			];
			for (const rootReadmePath of rootReadmePaths) if (await exists(rootReadmePath)) {
				const readReadme = await (0, node_fs_promises.readFile)(rootReadmePath, "utf-8");
				const { content } = (0, gray_matter.default)(readReadme);
				readme = content;
			}
			if (_configuration.title == null) {
				if (packageJSON == null) throw new Error(`\`title\` is required when \`package.json\` does not exist in the root directory (${rootPackageJSONPath})`);
				if (!packageJSON.name) throw new Error(`\`title\` is required when \`name\` does not exist in the root package.json`);
				_configuration.title = packageJSON.name;
			}
			if (_configuration.license == null) {
				if (packageJSON == null) throw new Error(`\`license\` is required when \`package.json\` does not exist in the root directory (${rootPackageJSONPath})`);
				if (!packageJSON.license) throw new Error(`\`license\` is required when \`license\` does not exist in the root package.json`);
				const license = String(packageJSON.license).toLowerCase();
				if (!licenseValues.includes(license)) throw new Error(`Auto discovered license \`${license}\` in \`package.json\` is not supported. Must be one of licenses: ${licenseValues.join(", ")} to specified in \`configuration\` argument`);
				_configuration.license = license;
			}
			if (_configuration.sdk == null) _configuration.sdk = "static";
			if (_configuration.short_description && _configuration.short_description.length > 60) throw new Error("short_description must be less or equal to 60 characters");
		},
		async generateBundle() {
			if (readme == null) readme = `# ${_configuration.title}`;
			this.emitFile({
				type: "asset",
				fileName: "README.md",
				source: gray_matter.default.stringify({ content: `\n${readme}` }, _configuration)
			});
		}
	};
}

//#endregion
exports.LFS = LFS
exports.SpaceCard = SpaceCard