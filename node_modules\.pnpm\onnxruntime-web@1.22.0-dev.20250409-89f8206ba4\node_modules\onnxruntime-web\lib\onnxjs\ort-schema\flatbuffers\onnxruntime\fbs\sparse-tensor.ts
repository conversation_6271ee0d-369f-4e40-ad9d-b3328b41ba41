// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

import { Tensor } from '../../onnxruntime/fbs/tensor.js';

export class SparseTensor {
  bb: flatbuffers.ByteBuffer | null = null;
  bb_pos = 0;
  __init(i: number, bb: flatbuffers.ByteBuffer): SparseTensor {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }

  static getRootAsSparseTensor(bb: flatbuffers.ByteBuffer, obj?: SparseTensor): SparseTensor {
    return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  static getSizePrefixedRootAsSparseTensor(bb: flatbuffers.ByteBuffer, obj?: SparseTensor): SparseTensor {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }

  values(obj?: Tensor): Tensor | null {
    const offset = this.bb!.__offset(this.bb_pos, 4);
    return offset ? (obj || new Tensor()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
  }

  indices(obj?: Tensor): Tensor | null {
    const offset = this.bb!.__offset(this.bb_pos, 6);
    return offset ? (obj || new Tensor()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
  }

  dims(index: number): bigint | null {
    const offset = this.bb!.__offset(this.bb_pos, 8);
    return offset ? this.bb!.readInt64(this.bb!.__vector(this.bb_pos + offset) + index * 8) : BigInt(0);
  }

  dimsLength(): number {
    const offset = this.bb!.__offset(this.bb_pos, 8);
    return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
  }

  static startSparseTensor(builder: flatbuffers.Builder) {
    builder.startObject(3);
  }

  static addValues(builder: flatbuffers.Builder, valuesOffset: flatbuffers.Offset) {
    builder.addFieldOffset(0, valuesOffset, 0);
  }

  static addIndices(builder: flatbuffers.Builder, indicesOffset: flatbuffers.Offset) {
    builder.addFieldOffset(1, indicesOffset, 0);
  }

  static addDims(builder: flatbuffers.Builder, dimsOffset: flatbuffers.Offset) {
    builder.addFieldOffset(2, dimsOffset, 0);
  }

  static createDimsVector(builder: flatbuffers.Builder, data: bigint[]): flatbuffers.Offset {
    builder.startVector(8, data.length, 8);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addInt64(data[i]!);
    }
    return builder.endVector();
  }

  static startDimsVector(builder: flatbuffers.Builder, numElems: number) {
    builder.startVector(8, numElems, 8);
  }

  static endSparseTensor(builder: flatbuffers.Builder): flatbuffers.Offset {
    const offset = builder.endObject();
    return offset;
  }
}
