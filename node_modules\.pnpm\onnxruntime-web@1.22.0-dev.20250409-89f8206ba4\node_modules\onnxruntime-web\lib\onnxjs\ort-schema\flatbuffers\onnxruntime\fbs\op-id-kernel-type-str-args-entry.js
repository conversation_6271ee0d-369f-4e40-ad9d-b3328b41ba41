'use strict';
// automatically generated by the FlatBuffers compiler, do not modify
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        var desc = Object.getOwnPropertyDescriptor(m, k);
        if (!desc || ('get' in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k];
            },
          };
        }
        Object.defineProperty(o, k2, desc);
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      });
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, 'default', { enumerable: true, value: v });
      }
    : function (o, v) {
        o['default'] = v;
      });
var __importStar =
  (this && this.__importStar) ||
  function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null)
      for (var k in mod)
        if (k !== 'default' && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.OpIdKernelTypeStrArgsEntry = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */
const flatbuffers = __importStar(require('flatbuffers'));
const kernel_type_str_args_entry_js_1 = require('../../onnxruntime/fbs/kernel-type-str-args-entry.js');
class OpIdKernelTypeStrArgsEntry {
  constructor() {
    this.bb = null;
    this.bb_pos = 0;
  }
  __init(i, bb) {
    this.bb_pos = i;
    this.bb = bb;
    return this;
  }
  static getRootAsOpIdKernelTypeStrArgsEntry(bb, obj) {
    return (obj || new OpIdKernelTypeStrArgsEntry()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  static getSizePrefixedRootAsOpIdKernelTypeStrArgsEntry(bb, obj) {
    bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
    return (obj || new OpIdKernelTypeStrArgsEntry()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
  }
  opId(optionalEncoding) {
    const offset = this.bb.__offset(this.bb_pos, 4);
    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
  }
  kernelTypeStrArgs(index, obj) {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset
      ? (obj || new kernel_type_str_args_entry_js_1.KernelTypeStrArgsEntry()).__init(
          this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4),
          this.bb,
        )
      : null;
  }
  kernelTypeStrArgsLength() {
    const offset = this.bb.__offset(this.bb_pos, 6);
    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
  }
  static startOpIdKernelTypeStrArgsEntry(builder) {
    builder.startObject(2);
  }
  static addOpId(builder, opIdOffset) {
    builder.addFieldOffset(0, opIdOffset, 0);
  }
  static addKernelTypeStrArgs(builder, kernelTypeStrArgsOffset) {
    builder.addFieldOffset(1, kernelTypeStrArgsOffset, 0);
  }
  static createKernelTypeStrArgsVector(builder, data) {
    builder.startVector(4, data.length, 4);
    for (let i = data.length - 1; i >= 0; i--) {
      builder.addOffset(data[i]);
    }
    return builder.endVector();
  }
  static startKernelTypeStrArgsVector(builder, numElems) {
    builder.startVector(4, numElems, 4);
  }
  static endOpIdKernelTypeStrArgsEntry(builder) {
    const offset = builder.endObject();
    builder.requiredField(offset, 4); // op_id
    return offset;
  }
  static createOpIdKernelTypeStrArgsEntry(builder, opIdOffset, kernelTypeStrArgsOffset) {
    OpIdKernelTypeStrArgsEntry.startOpIdKernelTypeStrArgsEntry(builder);
    OpIdKernelTypeStrArgsEntry.addOpId(builder, opIdOffset);
    OpIdKernelTypeStrArgsEntry.addKernelTypeStrArgs(builder, kernelTypeStrArgsOffset);
    return OpIdKernelTypeStrArgsEntry.endOpIdKernelTypeStrArgsEntry(builder);
  }
}
exports.OpIdKernelTypeStrArgsEntry = OpIdKernelTypeStrArgsEntry;
//# sourceMappingURL=op-id-kernel-type-str-args-entry.js.map
