{"version": 3, "file": "reduce.js", "sourceRoot": "", "sources": ["reduce.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAEvG,kDAAkG;AAElG,wCAA0C;AAE1C,oCAAqE;AAUrE,MAAM,MAAM,GAAG,CACb,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAC5B,IAAY,EACZ,QAAkB,EACR,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,qBAAqB,GAAG;QAC5B,IAAI;QACJ,UAAU,EAAE,CAAC,GAAG,CAAC;QACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;KACnC,CAAC;IAEF,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CACjC;QACE,GAAG,qBAAqB;QACxB,SAAS,EAAE,UAAU,CAAC,QAAQ;QAC9B,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC;KAChH,EACD,MAAM,CACP,CAAC;IACF,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAEK,MAAM,qBAAqB,GAA6C,CAAC,IAAgB,EAAoB,EAAE;IACpH,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7D,OAAO,IAAA,sDAA2B,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;AACzD,CAAC,CAAC;AAJW,QAAA,qBAAqB,yBAIhC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,QAA+B,EAC/B,MAAgB,EAChB,UAA4B,EAC5B,KAAa,EACb,QAAkB,EAClB,qBAAsC,EACzB,EAAE;IACf,MAAM,WAAW,GAAa,EAAE,CAAC;IACjC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;IAEzC,MAAM,OAAO,GAAG,EAAE,CAAC,CAAC,uCAAuC;IAE3D,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7E,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACnC,IAAI,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9C,0BAA0B;QAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAI,UAAU,CAAC,QAAQ,EAAE;gBACvB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACrB,CAAC,6CAA6C;YAE/C,0BAA0B;YAC1B,SAAS,GAAG;qBACG,CAAC,UAAU,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;uBACxC,CAAC,QAAQ,CAAC;cACnB,SAAS;YACX,CAAC;SACR;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;YAEnE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;KACF;IAED,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;IAEtC,MAAM,YAAY,GAAG;oCACa,KAAK;;uBAElB,KAAK;UAClB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;UAClB,GAAG,CAAC,CAAC,CAAC;UACN,SAAS;UACT,GAAG,CAAC,CAAC,CAAC;;QAER,CAAC;IAEP,OAAO;QACL,GAAG,qBAAqB;QACxB,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,iDAAiD;IACjD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,IAAI,wBAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/C,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAEK,MAAM,SAAS,GAA6C,CACjE,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,GAAa,EAAE,CAAC,CAAC,cAAc,EAAE,wBAAwB,EAAE,EAAE,CAAC,CAAC;IAC1F,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC7E,CAAC,CAAC;AAPW,QAAA,SAAS,aAOpB;AAEK,MAAM,UAAU,GAA6C,CAClE,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,CAAC,MAAgB,EAAE,IAAc,EAAY,EAAE;QACxE,IAAI,IAAI,GAAG,GAAG,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7C,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAC3B;SACF;QAED,OAAO,CAAC,cAAc,EAAE,wBAAwB,EAAE,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,8BAA8B;IACzG,CAAC,CAAC;IACF,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAC9E,CAAC,CAAC;AAhBW,QAAA,UAAU,cAgBrB;AAEK,MAAM,SAAS,GAA6C,CACjE,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,CAAC,MAAgB,EAAE,IAAc,EAAY,EAAE;QACxE,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB;aACtD;SACF;QAED,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,EAAE,CAAC,CAAC;IACnG,CAAC,CAAC;IACF,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC7E,CAAC,CAAC;AAhBW,QAAA,SAAS,aAgBpB;AAEK,MAAM,SAAS,GAA6C,CACjE,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,CAAC,MAAgB,EAAE,IAAc,EAAY,EAAE;QACxE,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB;aACtD;SACF;QAED,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,EAAE,CAAC,CAAC;IACnG,CAAC,CAAC;IACF,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC7E,CAAC,CAAC;AAhBW,QAAA,SAAS,aAgBpB;AAEK,MAAM,UAAU,GAA6C,CAClE,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,GAAa,EAAE,CAAC,CAAC,cAAc,EAAE,wBAAwB,EAAE,EAAE,CAAC,CAAC;IAC1F,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAC9E,CAAC,CAAC;AAPW,QAAA,UAAU,cAOrB;AAEK,MAAM,YAAY,GAA6C,CACpE,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,GAAa,EAAE,CAAC,CAAC,cAAc,EAAE,wBAAwB,EAAE,qBAAqB,CAAC,CAAC;IAC7G,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;AAChF,CAAC,CAAC;AAPW,QAAA,YAAY,gBAOvB;AAEK,MAAM,kBAAkB,GAA6C,CAC1E,gBAAuC,EACvC,MAAgB,EAChB,UAA4B,EAClB,EAAE;IACZ,MAAM,QAAQ,GAAa,GAAa,EAAE,CAAC,CAAC,uBAAuB,EAAE,mCAAmC,EAAE,EAAE,CAAC,CAAC;IAC9G,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,QAAQ,CAAC,CAAC;AACtF,CAAC,CAAC;AAPW,QAAA,kBAAkB,sBAO7B"}