{"version": 3, "file": "unary-op.js", "sourceRoot": "", "sources": ["unary-op.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAGvG,wCAAmD;AACnD,0DAAsE;AACtE,gDAAyC;AAEzC,oCAAwF;AAExF,SAAgB,OAAO;IACrB,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAFD,0BAEC;AACD,SAAgB,QAAQ;IACtB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAFD,4BAEC;AACD,SAAgB,QAAQ;IACtB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAFD,4BAEC;AACD,SAAgB,QAAQ;IACtB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAFD,4BAEC;AACD,SAAgB,QAAQ;IACtB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAFD,4BAEC;AACD,SAAgB,OAAO;IACrB,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAFD,0BAEC;AACD,SAAgB,OAAO,CAAC,KAAa;IACnC,MAAM,IAAI,GAAG,KAAK,CAAC;IACnB,MAAM,IAAI,GAAG;8BACe,KAAK;;UAEzB,IAAI;;;SAGL,IAAI;kBACK,IAAI,WAAW,IAAI,WAAW,IAAI,WAAW,IAAI;;GAEhE,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAbD,0BAaC;AACD,SAAgB,OAAO;IACrB,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAFD,0BAEC;AACD,SAAgB,SAAS;IACvB,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC;AAFD,8BAEC;AACD,SAAgB,QAAQ,CAAC,GAAW,EAAE,GAAW;IAC/C,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;4BACa,GAAG;4BACH,GAAG;;UAErB,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAdD,4BAcC;AACD,SAAgB,YAAY;IAC1B,MAAM,IAAI,GAAG,WAAW,CAAC;IACzB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,oCAWC;AACD,SAAgB,aAAa,CAAC,KAAa;IACzC,MAAM,IAAI,GAAG,WAAW,CAAC;IACzB,MAAM,IAAI,GAAG;8BACe,KAAK;;UAEzB,IAAI;;;SAGL,IAAI;kBACK,IAAI,WAAW,IAAI,WAAW,IAAI,WAAW,IAAI;;GAEhE,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAbD,sCAaC;AACD,SAAgB,OAAO;IACrB,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAFD,0BAEC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,KAAK,CAAC;IACnB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,0BAWC;AACD,SAAgB,OAAO;IACrB,MAAM,IAAI,GAAG,KAAK,CAAC;IACnB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;SAGJ,IAAI;;;UAGH,IAAI;;;GAGX,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAjBD,0BAiBC;AACD,SAAgB,OAAO;IACrB,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAFD,0BAEC;AACD,SAAgB,QAAQ;IACtB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,4BAWC;AACD,SAAgB,WAAW;IACzB,MAAM,IAAI,GAAG,SAAS,CAAC;IACvB,MAAM,IAAI,GAAG;UACL,IAAI;;;SAGL,IAAI;;;GAGV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAXD,kCAWC;AACD,SAAgB,QAAQ;IACtB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAFD,4BAEC;AACD,SAAgB,OAAO;IACrB,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAFD,0BAEC;AACD,SAAgB,QAAQ;IACtB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,IAAI,GAAG;UACL,IAAI;;;;;SAKL,IAAI;;;;;GAKV,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAfD,4BAeC;AACD,SAAS,gBAAgB,CAAC,IAAY;IACpC,MAAM,IAAI,GAAG;UACL,IAAI;aACD,IAAI;;SAER,IAAI;aACA,IAAI;;GAEd,CAAC;IACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,+BAAY,CAAC,UAAU,EAAE,CAAC;AACvD,CAAC;AAED,KAAK;AACL,KAAK;AACL,KAAK;AAEL,MAAM,4BAA4B,GAAG,CACnC,OAA8B,EAC9B,QAAyB,EACzB,KAAa,EACb,QAA2B,EACd,EAAE;IACf,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,CAAC;IACrF,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAChE,OAAO;QACL,GAAG,QAAQ;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE;QAC3D,YAAY,EAAE;OACX,QAAQ,CAAC,IAAI;;kBAEF,IAAI,CAAC,SAAS;aACnB,QAAQ,CAAC,IAAI;SACjB,IAAI,CAAC,MAAM;;MAEd;QACF,OAAO,EAAE,IAAI;KACd,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,kCAAkC,GAAG,CACzC,OAA8B,EAC9B,KAAa,EACb,QAA2B,EAC3B,QAAiB,EACE,EAAE;IACrB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,CAAC;IACrF,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;IAC5G,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,4BAA4B,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;AACtG,CAAC,CAAC;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAOK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAE,UAA0B,EAAY,EAAE,CAAC;IAC9G,OAAO,CAAC,GAAG,CACT,kCAAkC,CAChC,OAAO,EACP,MAAM,CAAC,CAAC,CAAC,EACT,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EACxC,UAAU,CAAC,QAAQ,CACpB,EACD,MAAM,CACP;CACF,CAAC;AAVW,QAAA,IAAI,QAUf;AAEK,MAAM,mBAAmB,GAAG,CAAC,IAAgB,EAAkB,EAAE,CACtE,IAAA,sDAA2B,EAAC;IAC1B,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,eAAQ,CAAC;IAC9C,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,eAAQ,CAAC;CAC/C,CAAC,CAAC;AAJQ,QAAA,mBAAmB,uBAI3B;AAEE,MAAM,OAAO,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE;IACpF,MAAM,UAAU,GAAG,gCAAgC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrE,OAAO,IAAA,YAAI,EAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAChD,CAAC,CAAC;AAHW,QAAA,OAAO,WAGlB;AAEF,MAAM,gCAAgC,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAkB,EAAE;IAC5G,IACE,MAAM,CAAC,MAAM,IAAI,CAAC;QAClB,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EACtG;QACA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAQ,CAAC;IACpE,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAQ,CAAC;IACpE,OAAO,IAAA,sDAA2B,EAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACnD,CAAC,CAAC;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAMK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAE,UAAyB,EAAY,EAAE,CAAC;IAC5G,OAAO,CAAC,GAAG,CACT,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,EACtG,MAAM,CACP;CACF,CAAC;AALW,QAAA,GAAG,OAKd;AAEK,MAAM,kBAAkB,GAAG,CAAC,IAAgB,EAAiB,EAAE,CACpE,IAAA,sDAA2B,EAAC,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AADpE,QAAA,kBAAkB,sBACkD;AAE1E,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,KAAK,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC;CACzF,CAAC;AAFW,QAAA,KAAK,SAEhB;AAEK,MAAM,QAAQ,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACtF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,CAAC;CAC5F,CAAC;AAFW,QAAA,QAAQ,YAEnB;AAMK,MAAM,SAAS,GAAG,CACvB,OAA8B,EAC9B,MAAgB,EAChB,UAA+B,EACrB,EAAE,CAAC;IACb,OAAO,CAAC,GAAG,CACT,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,EAC5G,MAAM,CACP;CACF,CAAC;AATW,QAAA,SAAS,aASpB;AAEK,MAAM,wBAAwB,GAAG,CAAC,IAAgB,EAAuB,EAAE,CAChF,IAAA,sDAA2B,EAAC,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AADrE,QAAA,wBAAwB,4BAC6C;AAE3E,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,OAAO,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACrF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,MAAM,CAAC;CAC3F,CAAC;AAFW,QAAA,OAAO,WAElB;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,GAAG,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC;CACvF,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,IAAI,GAAG,CAAC,OAA8B,EAAE,MAAgB,EAAY,EAAE,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;CACxF,CAAC;AAFW,QAAA,IAAI,QAEf"}